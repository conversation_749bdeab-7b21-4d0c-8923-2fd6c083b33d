import { Injectable } from '@angular/core';
import { AgentData, AgentInput } from '../pages/workflows/workflow-execution/components/workflow-playground/workflow-playground.component';

export interface PipelineAgent {
  serial: number;
  agent: {
    id: number;
    name: string;
    role: string;
    goal: string;
    backstory: string;
    task: {
      description: string;
      expectedOutput: string;
    };
  };
}

export interface ExtractedInput {
  placeholder: string;
  inputName: string;
  inputType: 'text' | 'image';
  agentNames: string[];
}

@Injectable({
  providedIn: 'root'
})
export class WorkflowInputExtractorService {
  private readonly PLACEHOLDER_PATTERNS = /(%\d+\$s)|\{\{([a-zA-Z0-9-_]+)\}\}/g;

  constructor() { }

  /**
   * Extract input fields from pipeline agents using placeholder pattern extraction
   */
  public extractInputFields(pipeLineAgents: PipelineAgent[]): ExtractedInput[] {
    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};

    pipeLineAgents.forEach((agent: PipelineAgent) => {
      const agentName = agent?.agent?.name;
      const agentDescription = agent?.agent?.task?.description;
      
      if (!agentDescription) return;

      const matches = agentDescription.matchAll(this.PLACEHOLDER_PATTERNS) || [];

      for (const match of matches) {
        const placeholder = match[1] || match[2];
        const placeholderInput = match[0];
        
        if (!placeholderMap[placeholder]) {
          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };
        }
        
        placeholderMap[placeholder].agents.add(agentName);
        placeholderMap[placeholder].inputs.add(placeholderInput);
      }
    });

    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({
      placeholder,
      inputName: this.formatInputName(placeholder),
      inputType: this.determineInputType(placeholder, [...inputs][0]),
      agentNames: [...agents]
    }));
  }

  /**
   * Convert pipeline agents to AgentData format with extracted inputs
   */
  public convertToAgentData(pipeLineAgents: PipelineAgent[]): AgentData[] {
    const extractedInputs = this.extractInputFields(pipeLineAgents);
    
    return pipeLineAgents.map((pipelineAgent: PipelineAgent) => {
      const agent = pipelineAgent.agent;
      
      // Find inputs that belong to this agent
      const agentInputs = extractedInputs.filter(input => 
        input.agentNames.includes(agent.name)
      ).map(input => ({
        placeholder: input.placeholder,
        inputName: input.inputName,
        inputType: input.inputType,
        value: '',
        files: []
      }));

      return {
        id: agent.id,
        name: agent.name,
        role: agent.role,
        goal: agent.goal,
        backstory: agent.backstory,
        task: agent.task,
        serial: pipelineAgent.serial,
        inputs: agentInputs,
        hasInputs: agentInputs.length > 0
      };
    });
  }

  /**
   * Determine if input is for image or text based on placeholder name
   */
  private determineInputType(placeholder: string, input: string): 'text' | 'image' {
    const variableName = placeholder.toLowerCase();
    
    // Check if the placeholder name suggests it's an image
    if (variableName.includes('image') || 
        variableName.includes('img') || 
        variableName.includes('photo') || 
        variableName.includes('picture')) {
      return 'image';
    }

    // Check the input pattern for image indicators
    const match = input.match(/\{\{(.*?)\}\}/);
    if (match && match[1]) {
      const matchedVariable = match[1].trim().toLowerCase();
      if (matchedVariable.includes('image') || 
          matchedVariable.includes('img') || 
          matchedVariable.includes('photo') || 
          matchedVariable.includes('picture')) {
        return 'image';
      }
    }

    return 'text';
  }

  /**
   * Format placeholder name to a user-friendly input name
   */
  private formatInputName(placeholder: string): string {
    // Remove special characters and convert to title case
    return placeholder
      .replace(/[_-]/g, ' ')
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  /**
   * Check if an agent has any input requirements
   */
  public agentHasInputs(agent: PipelineAgent, extractedInputs: ExtractedInput[]): boolean {
    return extractedInputs.some(input => input.agentNames.includes(agent.agent.name));
  }

  /**
   * Get inputs for a specific agent
   */
  public getAgentInputs(agentName: string, extractedInputs: ExtractedInput[]): AgentInput[] {
    return extractedInputs
      .filter(input => input.agentNames.includes(agentName))
      .map(input => ({
        placeholder: input.placeholder,
        inputName: input.inputName,
        inputType: input.inputType,
        value: '',
        files: []
      }));
  }

  /**
   * Validate that all required inputs have values
   */
  public validateInputs(agents: AgentData[]): { isValid: boolean; missingInputs: string[] } {
    const missingInputs: string[] = [];

    agents.forEach(agent => {
      if (agent.hasInputs && agent.inputs) {
        agent.inputs.forEach(input => {
          if (input.inputType === 'text' && !input.value?.trim()) {
            missingInputs.push(`${agent.name} - ${input.inputName}`);
          } else if (input.inputType === 'image' && (!input.files || input.files.length === 0)) {
            missingInputs.push(`${agent.name} - ${input.inputName} (Image required)`);
          }
        });
      }
    });

    return {
      isValid: missingInputs.length === 0,
      missingInputs
    };
  }
}
