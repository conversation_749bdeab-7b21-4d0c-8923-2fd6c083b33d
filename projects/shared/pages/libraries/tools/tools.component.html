<div id="tools-container" class="container-fluid">
  <div id="search-filter-container" class="row g-3">
    <div class="col-12 col-md-8 col-lg-9 col-xl-10 search-section">
      <form [formGroup]="searchForm">
        <ava-textbox
          placeholder='Search "Tools"'
          hoverEffect="glow"
          pressedEffect="solid"
          formControlName="search"
        >
          <ava-icon
            slot="icon-start"
            iconName="search"
            [iconSize]="16"
            iconColor="var(--color-brand-primary)"
          >
          </ava-icon>
        </ava-textbox>
      </form>
    </div>
    <div class="col-12 col-md-4 col-lg-3 col-xl-2 action-buttons">
      <ava-dropdown
        dropdownTitle="choose tool"
        [options]="toolsOptions"
        (selectionChange)="onSelectionChange($event)"
      >
      </ava-dropdown>
    </div>
  </div>

  <div class="build-in-tools">
    <ava-button
      label="All"
      [variant]="selectedToolFilter === 'all' ? 'primary' : 'secondary'"
      [pill]="true"
      size="small"
      (userClick)="showAllTools()"
    ></ava-button>
    <ava-button
      label="Built In"
      [variant]="selectedToolFilter === 'built-in' ? 'primary' : 'secondary'"
      [pill]="true"
      size="small"
      (userClick)="showBuiltInTools()"
    ></ava-button>
    <ava-button
      label="User Defined"
      [variant]="selectedToolFilter === 'user-defined' ? 'primary' : 'secondary'"
      [pill]="true"
      size="small"
      (userClick)="showUserDefinedTools()"
    ></ava-button>
  </div>

  <div id="prompts-card-container" class="row g-3">
    <!-- Hide Create New card when viewing Built In tools -->
    <ava-text-card
      *ngIf="selectedToolFilter !== 'built-in'"
      class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      [title]="labels.createNew"
      (cardClick)="onCreateTool()"
      [isLoading]="isLoading"
    >
    </ava-text-card>

    <!-- No Results Message -->
    <div
      class="col-12 d-flex justify-content-center align-items-center py-5"
      *ngIf=" !isLoading && filteredTools.length === 0"
    >
      <div class="text-center">
        <h5 class="text-muted">{{ labels.noResults }}</h5>
      </div>
    </div>

  <ng-container *ngFor="let tool of isLoading && displayedTools.length === 0 ? cardSkeletonPlaceholders : displayedTools">
    <ava-console-card
      class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
      [title]="tool?.title || 'No Title'"
      [description]="tool?.description || 'No Description'"
      categoryIcon="wrench"
      categoryTitle="{{tool?.isBuiltIn ? 'Built-in' : 'User-defined'}}"
      categoryValue="42"
      [author]="tool?.owner || 'AAVA'"
      [date]="tool?.createdDate | timeAgo"
      [actions]="tool?.isBuiltIn ? builtInActions : userDefinedActions"
      (actionClick)="onActionClick($event, tool.id)"
      [skeleton]="isLoading"
      [class.built-in-card]="!isLoading && tool.isBuiltIn"
      [class.user-defined-card]="!isLoading && !tool.isBuiltIn"
    >
    </ava-console-card>
  </ng-container>
  </div>

  <!-- Pagination Footer -->
  <div class="row" *ngIf="filteredTools.length > 0">
    <div class="col-12 d-flex justify-content-center mt-4">
      <app-page-footer
        [totalItems]="getTotalItemsForPagination()"
        [currentPage]="currentPage"
        [itemsPerPage]="itemsPerPage"
        (pageChange)="onPageChange($event)"
      ></app-page-footer>
    </div>
  </div>
</div>

