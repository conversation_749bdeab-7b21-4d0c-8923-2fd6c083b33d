// Main layout styling
:host {
  height: 95%;
  display: flex;
  flex-direction: column;
}

main-layout {
  height: 100%;
  width: 100%;
  border: 1px solid var(--Brand-Neutral-n-100, #D1D3D8);
  ::ng-deep {
    .layout {
      background-color: #fafafa; // Changed to white
      width: 98.22%
    }
    
    .left-pane, .right-pane {
      background-color: #fafafa;
    }
  }
  .left-pane{
    width: 312px;
    height: 887px;

    .center-pane {
      width: 54%;
      background-color: #fafafa; // Changed to white to match image
      border-left: 1px solid #e9ecef;
      border-right: 1px solid #e9ecef;
    }
    
    .center-header {
      background-color: #ffffff; // Changed to white
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      
      .pane-title {
        font-size: 16px;
        font-weight: 600;
        color: #212529;
      }
    }
  }
}

// Header buttons in center pane - right aligned
.center-header-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-left: auto; // This pushes buttons to the right
}
.header-buttons{
  gap:4px;
  display: flex;
  padding:5px 0px 5px 0px;
}
// Left Pane: Tool Description
.tool-description-pane {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #fafafa;

  .form-field-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;

    &:last-child {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      ava-textarea {
        flex: 1;
        height: 100%;
        
        ::ng-deep .textarea-container {
          height: 100%;
          
          textarea {
            height: 100% !important;
            min-height: 120px;
            resize: vertical;
          }
        }
      }
    }
  }
}

// Center Pane: Tool Editor
.tool-editor-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff; // Changed to white background
  padding: 0; // Remove padding to match image

  .tool-class-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #e9ecef;
      background-color: #ffffff;
      
      .section-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #212529;
        flex: 1;
      }
      
      .run-btn {
        padding: 8px 16px;
        background-color: #ffffff;
        border: 1px solid #ced4da;
        border-radius: 6px;
        color: #495057;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-left: auto;
        
        &:hover {
          background-color: #f8f9fa;
          border-color: #adb5bd;
        }
      }
    }
    
    .editor-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-start;
      padding: 12px 20px;
      border-bottom: 1px solid #e9ecef;
      background-color: #ffffff;
      
      .editor-btn {
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &.secondary {
          background-color: #ffffff;
          border: 1px solid #ced4da;
          color: #495057;
          
          &:hover {
            background-color: #f8f9fa;
            border-color: #adb5bd;
          }
        }
      }
    }

    .tools-monaco-editor {
      flex: 1;
      display: flex;
      flex-direction: column;

      ::ng-deep {
        .editor-wrapper {
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        .monaco-editor-container {
          flex: 1;
          min-height: 300px;
        }

        .editor-footer {
          flex-shrink: 0;
          padding: 8px 12px;
          background-color: #f8f9fa;
          border-top: 1px solid #e9ecef;
          font-size: 12px;
          color: #6c757d;
        }
      }
    }
  }

  .validation-output-section {
    flex-shrink: 0;
    margin: 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .validation-json-editor {
      ::ng-deep {
        .monaco-editor-container {
          border: none;
          border-radius: 8px;
        }
      }
    }
  }
}

// Right Pane: Tool Playground
.tool-playground-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
  //background-color: #ffffff;
  //padding: 16px;

  .playground-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    app-playground {
      flex: 1;
      height: 100%;

      ::ng-deep {
        .playground-wrapper {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .chat-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          min-height: 0;
        }

        .chat-messages {
          flex: 1;
          overflow-y: auto;
          padding: 16px;
        }

        .chat-input-container {
          flex-shrink: 0;
          padding: 16px;
          border-top: 1px solid #e9ecef;
          background-color: #ffffff;
        }
      }
    }
  }
}

// Global styles for form elements
::ng-deep {
  // Textbox styling
  ava-textbox {
    .textbox-container {
      width: 100%;

      .textbox-label {
        font-weight: 600;
        color: #212529;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .textbox-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;

        &:focus {
          border-color: #215AD6;
          box-shadow: 0 0 0 0.2rem rgba(33, 90, 214, 0.25);
          outline: none;
        }

        &[readonly] {
          background-color: #f8f9fa;
          opacity: 1;
        }
      }
    }
  }

  // Textarea styling
  ava-textarea {
    .textarea-container {
      width: 100%;

      .textarea-label {
        font-weight: 600;
        color: #212529;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .textarea-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        font-family: inherit;
        resize: vertical;

        &:focus {
          border-color: #215AD6;
          box-shadow: 0 0 0 0.2rem rgba(33, 90, 214, 0.25);
          outline: none;
        }

        &[readonly] {
          background-color: #f8f9fa;
          opacity: 1;
        }
      }
    }
  }

  // Button styling
  ava-button {
    .btn {
      font-size: 14px;
      font-weight: 500;
      border-radius: 6px;
      transition: all 0.2s ease-in-out;

      &.btn-sm {
        padding: 6px 16px;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 1200px) {
  .tool-description-pane,
  .tool-editor-pane {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .button-row {
    flex-wrap: wrap;
    gap: 8px;
  }

  .tool-description-pane,
  .tool-editor-pane {
    padding: 8px;
  }
}

  .custom-center-header {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    background: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    z-index: 1;
  }

  .header-buttons {
    margin-right:0.8rem;
  }

:host ::ng-deep .tool-playground-pane,
:host ::ng-deep .tool-playground-pane .playground-container,
:host ::ng-deep .tool-playground-pane app-playground {
  height: 100vh !important;
  min-height: 0;
  max-height: 100vh !important;
  flex: 1 1 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.header-section {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.askava-container {
  width: 100%;
}

.place-end {
  display: flex;
  justify-content: flex-end;
}

.place-start {
  display: flex;
  justify-content: flex-start;
}

.place-space-between {
  display: flex;
  justify-content: space-between;
}

.actons {
  margin-top: 1rem;
  align-items: center;
}

.askava-container {
  // Overrding text aligns for the label
  &::ng-deep {
    label {
      text-align: start !important;
    }
  }
}

.generated-output__body {
  display: flex;
  flex-direction: column;
  row-gap: 1rem;
}


// Monaco editor inside pop was causing code to be placed on center 
// so fixing issue by overriding CSS
#outputEditor::ng-deep {
  .monaco-editor .view-lines {
    justify-content: flex-start !important;
    text-align: left !important;
  }
  .monaco-editor .sticky-scroll {
    justify-content: flex-start !important;
    text-align: left !important;
    left: 0 !important;
  }
  .monaco-editor .sticky-widget-lines-scrollable {
    left: 85px !important;
    text-align: start;
  }
}
// --------------------------------------------------