<p class="page-title">Guardrail Configuration</p>
<div class="create-guardrails-container">
  <form [formGroup]="guardrailForm">
    <div
      class="form-layout"
      [ngClass]="{ 'three-column-layout': isExecuteMode && showChatInterface }"
    >
      <!-- Left Column -->
      <div class="left-column" [class.collapsed]="isLeftCollapsed">
        <div class="left-header">
          <span class="left-title" *ngIf="!isLeftCollapsed">Guardrail Description</span>
          <lucide-icon name="panel-left" class="collapse-icon" (click)="toggleLeftPanel()"></lucide-icon>
        </div>
        <div class="card-content" *ngIf="!isLeftCollapsed">
          <ava-textbox
            [formControl]="getControl('name')"
            [label]="labels.guardrailName"
            id="guardrailName"
            name="GuardrailName"
            [placeholder]="labels.gnPlacholder"
            variant="primary"
            size="md"
            [fullWidth]="true"
            [required]="true"
            [error]="getFieldError('name')"
          ></ava-textbox>
          <ava-textarea
            id="description"
            name="description"
            [label]="labels.description"
            [formControl]="getControl('description')"
            [placeholder]="labels.gdPlaceholder"
            [rows]="4"
            size="md"
            [fullWidth]="true"
            [required]="true"
            [error]="getFieldError('description')"
          >
          </ava-textarea>
        </div>
      </div>

      <!-- Middle Column (split into Colang and YML panels) -->
      <div class="middle-column">
        <div class="split-editors">
          
          <div class="editors-header-row">
            <div class="editors-title">Guardrail Configuration</div>
            <ava-button
              [label]="isEditMode ? 'Update' : 'Save'"
              size="medium"
              state="active"
              variant="primary"
              (userClick)="isEditMode ? confirmUpdate() : confirmSave()"
              [disabled]="isSubmitDisabled()"
            ></ava-button>
          </div>
          <div class="colang-panel">
            <div class="code-editor-container">
              <app-code-editor
                #colangEditor
                [title]="'Colang Editor'"
                [titleRequired]="true"
                [language]="codeLanguagesMap['Colang']"
                [Control]="getColangControl()"
                [customCssClass]="'tools-monaco-editor'"
                [placeholder]="'Write your Colang code here...'"
                (valueChange)="onColangContentChanged($event)" 
              ></app-code-editor>
               <!-- Show error if touched and empty -->
          <div *ngIf="showCodeEditorError()" class="error-text">
            <ava-icon iconName="info" iconColor="red" iconSize="14"></ava-icon>
            {{ codeEditorState.errorMessage }}
          </div>
            </div>
          </div>
          <div class="yml-panel">
            <div class="code-editor-container">
              <app-code-editor
                #ymlEditor
                [title]="'YML Editor'"
                [language]="codeLanguagesMap['YML']"
                [Control]="getYmlControl()"
                [customCssClass]="'tools-monaco-editor'"
                [placeholder]="'Write your YML code here...'"
              ></app-code-editor>
            </div>
          </div>
        </div>
      </div>

      <!-- Rightmost Playground Panel (always visible) -->
      <div class="rightEnd-column">
        <!-- <div class="playground-title">Guardrail Playground</div> -->
        <app-playground
          [promptOptions]="promptOptions"
          [messages]="chatMessages"
          [isLoading]="isProcessingChat"
          [showChatInteractionToggles]="false"
          [showAiPrincipleToggle]="true"
          (promptChange)="onPromptChanged($event)"
          (messageSent)="handleChatMessage($event)"
          [showApprovalButton]="false"
          [isMinimalView]="true"
          [isDisabled]="isPlaygroundDisabled"
        >
        </app-playground>
      </div>
    </div>
  </form>
</div>

