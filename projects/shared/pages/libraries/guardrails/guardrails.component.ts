import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';
import { PaginationService } from '@shared/services/pagination.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  TextCardComponent,
  PopupComponent,
  DialogService
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import { Guardrail } from '@shared/models/card.model';
import { GuardrailsService } from '@shared/services/guardrails.service';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';
import guardrailsLabels from './constants/guardrails-base.json';
import { ConsoleCardAction, ConsoleCardComponent } from "@shared/components/console-card/console-card.component";
import { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';

@Component({
  selector: 'app-guardrails',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    IconComponent,
    DropdownComponent,
    PopupComponent,
    LucideAngularModule,
    ConsoleCardComponent,
    TimeAgoPipe,
],
  providers: [DatePipe],
  templateUrl: './guardrails.component.html',
  styleUrl: './guardrails.component.scss',
})
export class GuardrailsComponent implements OnInit {
  // Labels from constants file
  grLabels = guardrailsLabels.labels;

  searchForm!: FormGroup;
  search: any;
  onSearchClick() {
    throw new Error('Method not implemented.');
  }
  allGuardrails: any = [];
  filteredGuardrails: any[] = [];
  displayedGuardrails: any[] = [];
  isLoading: boolean = false;

  // Delete popup properties
  showDeletePopup: boolean = false;
  guardrailToDelete: any = null;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;
  guardrailsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;
defaultActions: ConsoleCardAction[] = [
    {
      id: 'edit',
      icon: 'edit',
      label: 'Edit item',
      tooltip: 'Edit',
    },
    {
      id: 'delete',
      icon: 'trash',
      label: 'Delete item',
      tooltip: 'Delete',
    },
    {
      id: 'run',
      icon: 'play',
      label: 'Run application',
      tooltip: 'Run',
      isPrimary: true,
    },
  ];
  showInfoPopup: boolean = false;
  infoMessage: string = '';
  message: string ="";
  cardSkeletonPlaceholders = Array(11);

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private route: ActivatedRoute,
    private datePipe: DatePipe,
    private guardrailsService: GuardrailsService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private dialogService: DialogService,
  ) {}

  ngOnInit(): void {
    this.searchForm = this.fb.group({
      search: [''],
    });
    this.filteredGuardrails = [...this.allGuardrails];
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      this.currentPage = parseInt(pageParam, 10);
    }
    this.filteredGuardrails = this.allGuardrails.map((item: any) => {
      const formattedDate =
        this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';
      return {
        ...item,
        createdDate: formattedDate,
      };
    });

    // initialize the search listener ONCE
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.filterGuardrails(searchText);
      });
    
    this.loadGuardrails();
  }

  loadGuardrails(): void {
    this.isLoading = true;
    this.guardrailsService
      .fetchAllGuardrails()
      .subscribe({
        next: (data: Guardrail[]) => {
          this.allGuardrails = data.map((item: Guardrail) => ({
            id: String(item.id),
            title: item.name,
            description: item.description || 'No description',
            tags: [
              { label: item.configKey, type: 'primary' },
              {
                label: `ChatBot: ${item.chatBot ? 'Yes' : 'No'}`,
                type: 'secondary',
              },
            ],
            actions: [
              {
                action: 'execute',
                icon: 'play_circle',
                tooltip: 'Run guardrail',
              },
              {
                action: 'clone',
                icon: 'content_copy',
                tooltip: 'Clone guardrail',
              },
              { action: 'delete', icon: 'delete', tooltip: 'Delete guardrail' },
            ],
            createdDate: new Date().toLocaleDateString(), // Optional: Use actual created date if available
          }));

          // after data is loaded, filter based on current search text
          const currentSearch =
            this.searchForm.get('search')?.value?.toLowerCase() || '';
          this.filterGuardrails(currentSearch);

          this.filteredGuardrails = [...this.allGuardrails];
          this.updateDisplayedGuardrails();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading guardrails:', error);
          this.isLoading = false;
          this.dialogService.error({
            title: 'Loading Failed',
            message: 'Failed to load guardrails. Please try again.',
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.loadGuardrails();
            }
          });
        }
      });
  }

  filterGuardrails(searchText: string): void {
    this.filteredGuardrails = this.allGuardrails.filter((gr: any) => {
      const inTitle = gr.title?.toLowerCase().includes(searchText);
      const inDescription = gr.description?.toLowerCase().includes(searchText);
      const inTags =
        Array.isArray(gr.tags) &&
        gr.tags?.some((tag: any) =>
          tag.label?.toLowerCase().includes(searchText),
        );

      return inTitle || inDescription || inTags;
    });

    this.updateDisplayedGuardrails();
  }

  updateDisplayedGuardrails(): void {
    const result = this.paginationService.getPaginatedItems(
      this.filteredGuardrails,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedGuardrails = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateGuardrail(): void {
    this.router.navigate(['/libraries/guardrails/create']);
  }

  // onCardClicked(guardrailId: string): void {
  //   this.router.navigate(['/libraries/guardrails/edit', guardrailId], {
  //     queryParams: { returnPage: this.currentPage },
  //   });
  // }

  getHeaderIcons(guardrail: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'swords', title: guardrail.toolType || 'Guardrails' },
      { iconName: 'users', title: `${guardrail.userCount || 10}` },
    ];
  }

  getFooterIcons(guardrail: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'user', title: guardrail.owner || 'AAVA' },
      { iconName: 'calendar-days', title: guardrail.createdDate },
    ];
  }

  onActionClick(
    event: { actionId: string; action: ConsoleCardAction },
    guardrailId: string,
  ): void {
    switch (event.actionId) {
      case 'edit':
        this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);
        break;
      case 'delete':
        this.confirmDeleteGuardrail(guardrailId);
        break;
      case 'run':
        this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);
        break;
      case 'copy':
        this.duplicateGuardrail(guardrailId);
        break;
      default:
        break;
    }
  }

  confirmDeleteGuardrail(guardrailId: string): void {
    const guardrail = this.allGuardrails.find((item :any)=> item.id === guardrailId);
    if (!guardrail) return;

    this.dialogService.confirmation({
      title: 'Delete Guardrail',
      message: `Are you sure you want to delete "${guardrail.title}"? This action cannot be undone.`,
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'danger',
      icon:'trash',
    }).then(result => {
      if (result.confirmed) {
        this.deleteGuardrail(guardrailId);
      }
    });
  }

  deleteGuardrail(guardrailId: string): void {
    // Show loading dialog
    this.dialogService.loading({
      title: 'Deleting Guardrail...',
      message: 'Please wait while we delete the guardrail.',
      showProgress: false,
      showCancelButton: false
    });

    this.guardrailsService.deleteGuardrail(Number(guardrailId)).subscribe({
      next: (response) => {
        this.dialogService.close(); // Close loading dialog
        
        this.dialogService.success({
          title: 'Guardrail Deleted',
          message: 'Guardrail has been deleted successfully.'
        });

        // Remove the guardrail from local arrays
        this.allGuardrails = this.allGuardrails.filter((gr:any) => gr.id !== guardrailId);
        this.filteredGuardrails = this.filteredGuardrails.filter(gr => gr.id !== guardrailId);
        this.updateDisplayedGuardrails();
      },
      error: (err) => {
        this.dialogService.close(); // Close loading dialog
        
        const errorMessage = err?.error?.message || err?.message || 'Failed to delete guardrail. Please try again.';
        this.dialogService.error({
          title: 'Delete Failed',
          message: errorMessage,
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.deleteGuardrail(guardrailId);
          }
        });
      },
    });
  }

  // Remove old popup methods
  // closeDeletePopup(): void { ... }

  duplicateGuardrail(guardrailId: string): void {
    // Show loading dialog
    this.dialogService.loading({
      title: 'Duplicating Guardrail...',
      message: 'Please wait while we duplicate the guardrail.',
      showProgress: false,
      showCancelButton: false
    });

    // Navigate to create page with clone parameter
    setTimeout(() => {
      this.dialogService.close();
      this.router.navigate(['/libraries/guardrails/create'], {
        queryParams: { clone: guardrailId }
      });
    }, 1000);
  }

  getTagsLine(guardrail: any): string {
    if (!guardrail.tags || !Array.isArray(guardrail.tags)) return '';
    return guardrail.tags
      .map((tag: any) => tag.label?.trim())
      .filter((label: string | undefined) => !!label)
      .join(' | ');
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedGuardrails();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }
}
