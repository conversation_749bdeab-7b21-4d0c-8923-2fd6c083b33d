import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChangeDetectorRef } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
  FormArray,
  Validators,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ChatMessage } from '@shared/components/chat-window/chat-window.component';
import { MOCK_PROMPTS } from '@shared/mock-data/prompt-mock-data';
import { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';
import { Subscription } from 'rxjs';
import promptsLabels from '../constants/prompts.json';
import {
  AvaTextareaComponent,
  AvaTextboxComponent,
  ButtonComponent,
  AccordionComponent,
  PopupComponent,
  DropdownComponent,
  DialogService,
} from '@ava/play-comp-library';
import { SharedNavItemComponent } from '@shared/components/nav-item/nav-item.component';
import { PromptsService } from '@shared/services/prompts.service';
import { LucideAngularModule } from 'lucide-angular';
import { AskAvaWrapperComponent } from '@shared/components/ask-ava-wrapper/ask-ava-wrapper.component';
import { PromptEnhanceService } from '@shared/services/prompt-enhance.service';
import { PromptsModes, USE_CASE_IDENTIFIER } from '../constants/constants';
@Component({
  selector: 'app-create-prompts',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AvaTextboxComponent,
    AvaTextareaComponent,
    ButtonComponent,
    SharedNavItemComponent,
    AccordionComponent,
    PopupComponent,
    DropdownComponent,
    LucideAngularModule,
    AskAvaWrapperComponent,
  ],
  templateUrl: './create-prompts.component.html',
  styleUrls: ['./create-prompts.component.scss'],
})
export class CreatePromptsComponent implements OnInit, OnDestroy {
  isViewMode: boolean = false;
  promptTypeOptions: { name: string; value: string }[] = [];
  selectedPromptType: string = '';
  selectedDropdownValue: string = ''; // Add this property to track selected value
  selectedRoleValue: string = '';
  previousAccordionState: boolean = false;
  showSuccessPopup = false;
  submissionSuccess = false;
  popupTitle: string = '';
  popupMessage: string = ''
  formChanged = false;
  private isPatching = false;

  iconName = 'check-circle';
  @ViewChild('accordion') accordionRef!: AccordionComponent;
  // Labels used across the Prompt UI components (titles)
  public promptLabels = promptsLabels.labels;
  promptTabs = [
    { label: 'Freeform', value: 'freeform' },
    { label: 'Template', value: 'template' },
  ];

  showAskAvaModal = false;
  showPromptOutput = false;
  isLoading = false;


  selectedTab: string = 'freeform';

  onTabSelected(tabValue: string): void {
    this.selectedTab = tabValue;
    const promptTaskControl = this.promptForm.get('promptTask');
    if (tabValue === 'template') {
      promptTaskControl?.clearValidators();
    } else if (tabValue === 'freeform') {
      promptTaskControl?.setValidators([Validators.required]);
    }

    promptTaskControl?.updateValueAndValidity();
  }


  onPromptTypeChange(event: any): void {
    // Handle dropdown selection change
    if (event && event.selectedOptions && event.selectedOptions.length > 0) {
      const selectedValue = event.selectedOptions[0].value;
      this.promptForm
        .get('promptType')
        ?.setValue(selectedValue, { emitEvent: false });
      this.selectedDropdownValue = selectedValue;
    }
  }

  // Mode flags
  categoryId: number | null = null;
  promptId: string | null = null;
  isEditMode: boolean = false;
  isExecuteMode: boolean = false;
  showChatInterface: boolean = false;
  isLeftCollapsed = false;

  toggleLeftPanel() {
    this.isLeftCollapsed = !this.isLeftCollapsed;
  }

  promptForm: FormGroup;
  promptsOutputForm: FormGroup;

  // Chat interface properties
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;

  // Subscription
  private executionSubscription: Subscription = new Subscription();
  prompt = new FormControl('');

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService,
    private promptsService: PromptsService,
    private cdr: ChangeDetectorRef,
    private promptGenerateService: PromptEnhanceService,
    private dialogService: DialogService,
  ) {
    this.promptForm = this.createPromtForm();
    this.promptsOutputForm = this.createPromtForm();
  }

  promptCache: Record<string, string> = {
    template: '',
    freeform: '',
  };

  private createPromtForm(): FormGroup {
    return this.fb.group({
      // Prompt details
      name: [''],
      promptDescription: [''],
      // Prompt task (for freeform)
      promptTask: ['', Validators.required],
      // Right side (Template configuration fields)
      role: ['', Validators.required],
      backstory: [''],
      description: [''],
      goal: [''],
      expectedOutput: [''],
      // Prompt Type Dropdown
      promptType: [''],
      // Examples (these fields are still in the form group, but no longer rendered in HTML)
      examples: this.fb.array([
        this.fb.group({
          input: [''],
          output: [''],
        }),
      ]),
      addConsideration: [''],
      // Steps (these fields are still in the form group, but no longer rendered in HTML)
      step1Instruction: [''],
      // Add intermediateSteps to the form group
      intermediateSteps: [''],
    });
  }

  ngOnInit(): void {
    this.promptForm.valueChanges.subscribe(() => {
      if (!this.isPatching) {
        this.formChanged = true;
      }
    });
    // First load dropdown options
    this.promptsService.fetchPromptTypeDropdown().subscribe({
      next: (options) => {
        if (options && options.length && options[0].name && options[0].value) {
          this.promptTypeOptions = options;
        } else if (
          options &&
          typeof options === 'object' &&
          !Array.isArray(options)
        ) {
          this.promptTypeOptions = Object.keys(options).map((key) => ({
            name: options[key],
            value: options[key],
          }));
        } else {
          this.promptTypeOptions = [];
        }

        // After dropdown options are loaded, check if we need to load prompt data
        this.promptId = this.route.snapshot.paramMap.get('id');
        const executeParam = this.route.snapshot.queryParamMap.get('execute');
        const viewParam = this.route.snapshot.queryParamMap.get('view');

        this.isEditMode = !!this.promptId;
        this.isExecuteMode = executeParam === 'true';
        this.isViewMode = viewParam === 'true';
        this.showChatInterface = this.isExecuteMode;

        if (this.isEditMode && this.promptId) {
          this.loadPromptById(this.promptId);
        } else {
          // If not in edit mode but we have a stored prompt type, try to set it
          this.trySetStoredPromptType();
        }

        // If form is already patched but dropdown options were loaded later, try to update the dropdown
        this.tryUpdateDropdownAfterFormPatch();
      },
      error: (err) => {
        console.error('Dropdown API error:', err);
        // Even if dropdown fails, still try to load prompt data
        this.promptId = this.route.snapshot.paramMap.get('id');
        const executeParam = this.route.snapshot.queryParamMap.get('execute');
        const viewParam = this.route.snapshot.queryParamMap.get('view');

        setTimeout(() => {
          this.isEditMode = !!this.promptId;
          this.cdr.detectChanges(); // Optional: force update after flag is set
        });

        this.isExecuteMode = executeParam === 'true';
        this.isViewMode = viewParam === 'true';
        this.showChatInterface = this.isExecuteMode;

        if (this.isEditMode && this.promptId) {
          this.loadPromptById(this.promptId);
        }
      },
    });

    // Debug: log value changes for promptType
    this.promptForm.get('promptType')?.valueChanges.subscribe((val) => {
      // Update the selected dropdown value when form control changes
      this.selectedDropdownValue = val || '';
      console.log(
        'Form control value changed:',
        val,
        'selectedDropdownValue:',
        this.selectedDropdownValue,
      );
    });

    // Handle execute mode (this doesn't depend on dropdown options)
    const executeParam = this.route.snapshot.queryParamMap.get('execute');
    if (this.isExecuteMode && this.promptId) {
      this.chatMessages = [
        { from: 'ai', text: 'Hi Akash, this is the prompt testing' },
        { from: 'user', text: 'Test this input' },
        { from: 'ai', text: 'Here is the output' },
      ];

      setTimeout(() => {
        this.toolExecutionService.startExecution(
          this.promptId!,
          this.chatMessages,
        );
        this.executionSubscription = this.toolExecutionService
          .getExecutionState()
          .subscribe((state) => {
            if (state.isExecuting && state.toolId === this.promptId) {
              this.chatMessages = state.chatMessages;
            }
          });
      }, 100);
    }
  }

  // Add a property to store the prompt type for delayed setting
  private storedPromptType: string = '';

  // Method to try setting stored prompt type when dropdown options are loaded
  private trySetStoredPromptType(): void {
    if (this.storedPromptType && this.promptTypeOptions.length > 0) {
      const match = this.findMatchingDropdownOption(this.storedPromptType);
      if (match) {
        // Set the value immediately
        this.promptForm
          .get('promptType')
          ?.setValue(match.value, { emitEvent: false });
        this.selectedDropdownValue = match.value;

        // Also set it after a delay to ensure the dropdown is rendered
        setTimeout(() => {
          this.promptForm
            .get('promptType')
            ?.setValue(match.value, { emitEvent: false });
          this.selectedDropdownValue = match.value;
        }, 200);

        this.storedPromptType = ''; // Clear after setting
      }
    }
  }

  // Method to try updating dropdown after form is already patched
  private tryUpdateDropdownAfterFormPatch(): void {
    const currentValue = this.promptForm.get('promptType')?.value;
    if (currentValue && this.promptTypeOptions.length > 0) {
      // Check if the current value is not in the dropdown options
      const isValueInOptions = this.promptTypeOptions.some(
        (opt) => opt.value === currentValue || opt.name === currentValue,
      );

      if (!isValueInOptions) {
        // Try to find a matching option
        const match = this.findMatchingDropdownOption(currentValue);
        if (match) {
          // Set the value immediately
          this.promptForm
            .get('promptType')
            ?.setValue(match.value, { emitEvent: false });
          this.selectedDropdownValue = match.value;

          // Also set it after a delay to ensure the dropdown is rendered
          setTimeout(() => {
            this.promptForm
              .get('promptType')
              ?.setValue(match.value, { emitEvent: false });
            this.selectedDropdownValue = match.value;
          }, 200);
        }
      }
    }
  }

  // Helper method to find matching dropdown option
  private findMatchingDropdownOption(promptType: string): { name: string, value: string } | null {

    if (!promptType || this.promptTypeOptions.length === 0) {
      return null;
    }

    // Try exact match first (case-insensitive)
    let match = this.promptTypeOptions.find(
      (opt) => opt.value.toLowerCase() === promptType.toLowerCase(),
    );

    // If no exact match, try matching by name
    if (!match) {
      match = this.promptTypeOptions.find(
        (opt) => opt.name.toLowerCase() === promptType.toLowerCase(),
      );
    }

    // If still no match, try partial matching
    if (!match) {
      match = this.promptTypeOptions.find(
        opt => opt.value.toLowerCase().includes(promptType.toLowerCase()) ||
          opt.name.toLowerCase().includes(promptType.toLowerCase()) ||
          promptType.toLowerCase().includes(opt.value.toLowerCase()) ||
          promptType.toLowerCase().includes(opt.name.toLowerCase())
      );
    }

    return match || null;
  }

  private loadPromptById(promptId: string): void {
    this.promptsService.getPromptById(promptId).subscribe({
      next: (prompt) => {
        if (prompt) {
          this.categoryId = prompt.categoryId;
          this.patchPromptForm(prompt);
        } else {
        }
      },
      error: (err) => {
        console.error('Error fetching prompt:', err);
      },
    });
  }

  private patchPromptForm(prompt: any): void {
    this.isPatching = true;
    // After form is fully patched
    setTimeout(() => {
      this.isPatching = false;
      this.formChanged = false; // form hasn't changed yet
    }, 0);

    if (this.isViewMode) {
      setTimeout(() => {
        this.promptForm.disable({ emitEvent: false });
      }, 0);
    }

    // Set selected tab first based on type
    this.selectedTab = prompt.type === 'free form' ? 'freeform' : 'template';


    // Improved dropdown type matching logic
    let dropdownType = '';
    if (prompt.type) {
      if (this.promptTypeOptions.length > 0) {
        // Dropdown options are available, try to find a match
        const match = this.findMatchingDropdownOption(prompt.type);
        if (match) {
          dropdownType = match.value;
        } else {
          // If no match found, use the original type as fallback
          dropdownType = prompt.type;
          console.warn(
            `No matching dropdown option found for prompt type: "${prompt.type}". Available options:`,
            this.promptTypeOptions,
          );
        }
      } else {
        // Dropdown options not loaded yet, store the type for later
        this.storedPromptType = prompt.type;
        dropdownType = prompt.type;
      }
    } else {
      dropdownType = prompt.type || '';
    }

    // Patch only the relevant fields based on the selected tab/type
    const patchObj: any = {
      name: prompt.name || '',
      role: prompt.role || '',
      goal: prompt.goal || '',
      backstory: prompt.backstory || '',
      expectedOutput: prompt.expectedOutput || '',
      intermediateSteps: prompt.intermediateSteps || '',
      promptType: dropdownType,
    };

    if (this.selectedTab === 'freeform') {
      patchObj.promptTask = prompt.prompt || '';
      patchObj.promptDescription = prompt.promptDescription || '';
      // Clear template fields
      patchObj.description = prompt.description;
    } else if (this.selectedTab === 'template') {
      patchObj.description = prompt.description || '';
      patchObj.promptDescription = prompt.promptDescription || '';
      // Clear freeform fields
      patchObj.promptTask = '';
    }

    this.promptForm.patchValue(patchObj);

    // Update the selected dropdown value
    this.selectedDropdownValue = dropdownType;
    console.log('patchPromptForm - dropdownType:', dropdownType, 'selectedDropdownValue:', this.selectedDropdownValue);

    // If we're in template mode and have a dropdown type, ensure it's set after a delay
    if (this.selectedTab === 'template' && dropdownType) {
      setTimeout(() => {
        this.promptForm
          .get('promptType')
          ?.setValue(dropdownType, { emitEvent: false });
        this.selectedDropdownValue = dropdownType;
        console.log(
          'After delay - dropdownType:',
          dropdownType,
          'selectedDropdownValue:',
          this.selectedDropdownValue,
        );
      }, 100);
    }

    // Patch examples if available
    if (prompt.examples && Array.isArray(prompt.examples)) {
      const exampleArray = this.promptForm.get('examples') as FormArray;

      // Clear existing examples
      exampleArray.clear();

      // Push each example into the form array
      prompt.examples.forEach((ex: any) => {
        exampleArray.push(
          this.fb.group({
            input: [ex.exampleInput || ''],
            output: [ex.exampleOutput || ''],
          }),
        );
      });
    }
  }

  selectTab(tab: 'freeform' | 'template' | string) {
    this.selectedTab = tab as 'freeform' | 'template';
  }

  ngOnDestroy(): void {
    // Clean up subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
  }

  confirmSave(): void {
    this.dialogService.confirmation({
      title: 'Save Prompt?',
      message: 'Are you sure you want to save this prompt?',
      confirmButtonText: 'Save',
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'primary',
      icon: 'save'
    }).then(result => {
      if (result.confirmed) {
        this.onSave();
      }
    });
  }

  confirmUpdate(): void {
    this.dialogService.confirmation({
      title: 'Update Prompt?',
      message: 'Are you sure you want to update this prompt?',
      confirmButtonText: 'Update',
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'primary',
      icon: 'square-pen'
    }).then(result => {
      if (result.confirmed) {
        this.onSave();
      }
    });
  }

  onSave(): void {
    this.formChanged = false;

    if (this.isViewMode) {
      return;
    }
    const nameControl = this.promptForm.get('name');
    const promptTaskControl = this.promptForm.get('promptTask');
    const promptTypeControl = this.promptForm.get('promptType');
    const promptDescControl = this.promptForm.get('promptDescription');
    const expectedOutputControl = this.promptForm.get('expectedOutput');
    const roleControl = this.promptForm.get('role');
    const goalControl = this.promptForm.get('goal');
    const backstoryControl = this.promptForm.get('backstory');
    const stepsControl = this.promptForm.get('intermediateSteps');
    //  Clear all validators first
    stepsControl?.clearValidators();
    nameControl?.clearValidators();
    promptTaskControl?.clearValidators();
    promptTypeControl?.clearValidators();
    promptDescControl?.clearValidators();
    expectedOutputControl?.clearValidators();
    roleControl?.clearValidators();
    goalControl?.clearValidators();
    backstoryControl?.clearValidators();

    // Apply required validators
    nameControl?.setValidators([Validators.required]);
    promptDescControl?.setValidators([Validators.required]);

    if (this.selectedTab === 'template') {
      promptDescControl?.setValidators([Validators.required]);
      expectedOutputControl?.setValidators([Validators.required]);
      roleControl?.setValidators([Validators.required]);
      goalControl?.setValidators([Validators.required]);
      backstoryControl?.setValidators([Validators.required]);
      promptTypeControl?.setValidators([Validators.required]);
      promptTaskControl?.clearValidators();
      promptTaskControl?.setValue('');
    } else if (this.selectedTab === 'freeform') {
      promptTaskControl?.setValidators([Validators.required]);
      this.promptForm.get('promptType')?.setValue('free form', { emitEvent: false });
    }

    //  Update value and validity
    nameControl?.updateValueAndValidity();
    promptTaskControl?.updateValueAndValidity();
    promptTypeControl?.updateValueAndValidity();
    promptDescControl?.updateValueAndValidity();
    expectedOutputControl?.updateValueAndValidity();
    roleControl?.updateValueAndValidity();
    goalControl?.updateValueAndValidity();
    backstoryControl?.updateValueAndValidity();

    if (this.promptForm.value.promptType === 'Chain of Thought') {
      stepsControl?.setValidators([Validators.required]);
    }
    stepsControl?.updateValueAndValidity();

    //  If invalid, mark touched and exit
    if (!this.promptForm.valid) {
      this.dialogService.warning({
        title: 'Form Incomplete',
        message: 'Please fill in all required fields before saving.',
        showProceedButton: false
      });
      this.promptForm.markAllAsTouched();
      return;
    }
    const formValues = this.promptForm.value;
    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;

    // Use the original case from dropdown for type
    const promptType = formValues.promptType || '';

    // Build payload for each type
    let createPayload: any = {
      categoryId: this.categoryId ?? 1, // fallback to 1 if null
      type: promptType,
      name: formValues.name,
      role: formValues.role,
      goal: formValues.goal,
      backstory: formValues.backstory,
      description: formValues.description,
      promptDescription: formValues.promptDescription,
      expectedOutput: formValues.expectedOutput,
      // For template prompts (including Zero Shot), use templateDescription as prompt
      prompt:
        this.selectedTab === 'template'
          ? formValues.description
          : formValues.promptTask || '',
    };

    if (promptType === 'Zero Shot') {
    } else if (
      promptType === 'One Shot/Multi Shot' ||
      promptType === 'One Shot' ||
      promptType === 'Multi Shot'
    ) {
      // Send all examples, including empty ones
      createPayload.examples = this.examples.getRawValue().map((e) => ({
        exampleInput: e.input,
        exampleOutput: e.output,
      }));
      createPayload.additionalConsideration = formValues.addConsideration;
    } else if (promptType === 'Chain of Thought') {
      createPayload.intermediateSteps = formValues.intermediateSteps;
      // Send all examples, including empty ones
      createPayload.examples = this.examples.getRawValue().map((e) => ({
        exampleInput: e.input,
        exampleOutput: e.output,
      }));
      createPayload.additionalConsideration = formValues.addConsideration;
    } else if (promptType === 'Free Form') {
      // For free form, only keep type, name, and prompt
      createPayload = {
        type: promptType,
        name: formValues.name,
        prompt: formValues.promptTask || '',
      };
    }

    // Remove undefined fields
    Object.keys(createPayload).forEach((key) => {
      if (createPayload[key] === undefined) {
        delete createPayload[key];
      }
    });
    // Show loading dialog
    this.dialogService.loading({
      title: this.isEditMode ? 'Updating Prompt...' : 'Creating Prompt...',
      message: `Please wait while we ${this.isEditMode ? 'update' : 'create'} the prompt.`,
      showProgress: false,
      showCancelButton: false
    });
    if (this.isEditMode && this.promptId) {
      // For edit, ensure id is present in payload
      const editPayload = { ...createPayload, id: this.promptId };
      this.promptsService.editPrompt(editPayload).subscribe({
        next: (info) => {
           this.dialogService.close(); // Close loading dialog
          this.dialogService.success({
            title: 'Success!',
            message: info?.info?.message || info.message || 'Prompt updated successfully!'
          }).then(() => {
            const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
            const pageNumber = returnPage ? parseInt(returnPage) : 1;
            this.router.navigate(['/libraries/prompts'], {
              queryParams: { page: pageNumber }
            });
          });
        },
        error: (error) => {
           this.dialogService.close(); // Close loading dialog
          this.dialogService.error({
            title: 'Update Failed',
            message: error?.error?.message || error.message || 'Failed to update prompt. Please try again.',
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.onSave(); // Retry the save operation
            }
          });
        },
      });
    } else {
      this.promptsService.addPrompt(createPayload).subscribe({
        next: (info) => {
           this.dialogService.close(); // Close loading dialog
          this.dialogService.success({
            title: 'Success!',
            message: info?.info?.message || info.message || 'Prompt created successfully!'
          }).then(() => {
            this.resetForm();
            const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
            const pageNumber = returnPage ? parseInt(returnPage) : 1;
            this.router.navigate(['/libraries/prompts'], {
              queryParams: { page: pageNumber }
            });
          });
        },
        error: (error) => {
           this.dialogService.close(); // Close loading dialog
          this.dialogService.error({
            title: 'Creation Failed',
            message: error?.error?.message || error.message || 'Failed to create prompt. Please try again.',
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.onSave(); // Retry the save operation
            }
          });
        },
      });
    }
  }

  onExecute(): void {
    if (!this.promptForm.valid) {
      this.dialogService.warning({
        title: 'Form Incomplete',
        message: 'Please complete the prompt configuration before executing.',
        showProceedButton: false
      });
      return;
    }

    if (this.promptId) {
      // If we're already in execute mode with chat interface showing
      if (this.isExecuteMode && this.showChatInterface) {
        // Process the execution
      } else {
        // Show loading dialog during execution setup
        this.dialogService.loading({
          title: 'Initializing Execution...',
          message: 'Setting up the prompt execution environment.',
          showProgress: false,
          showCancelButton: false
        });

        // Set flags to show chat interface
        this.isExecuteMode = true;
        this.showChatInterface = true;

        // Set the initial messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the prompt testing',
          },
          {
            from: 'user',
            text: 'Test this input',
          },
          {
            from: 'ai',
            text: 'Here is the output',
          },
        ];

        // Delay starting the execution service
        setTimeout(() => {
          this.dialogService.close(); // Close loading dialog
          this.toolExecutionService.startExecution(
            this.promptId!,
            this.chatMessages,
          );
        }, 1000);
      }
    }
  }

  onExit(): void {
    // If we're in execute mode with chat interface showing
    if (this.isExecuteMode && this.isEditMode) {
      // Return to edit mode without chat interface
      this.isExecuteMode = false;
      this.showChatInterface = false;
      this.toolExecutionService.stopExecution();
    } else {
      // Check if form has unsaved changes
      if (this.promptForm.dirty && !this.isViewMode) {
        this.dialogService.warning({
          title: 'Unsaved Changes',
          message: 'You have unsaved changes that will be lost if you continue. Are you sure you want to proceed?',
          showProceedButton: true,
          proceedButtonText: 'Proceed'
        }).then(result => {
          if (result.action === 'proceed') {
            this.navigateBack();
          }
        });
      } else {
        this.navigateBack();
      }
    }
  }

  private navigateBack(): void {
    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;

      // Exit to prompts list at correct page
    this.router.navigate(['/libraries/prompts'], {
        queryParams: { page: pageNumber },
    });
  }

  private resetForm(): void {
    // Reset examples array to a single empty group
    const examplesArray = this.promptForm.get('examples') as FormArray;
    while (examplesArray.length > 0) {
      examplesArray.removeAt(0);
    }
    examplesArray.push(this.fb.group({ input: [''], output: [''] }));
    
    // Reset form to initial state
    this.promptForm.reset();
    this.formChanged = false;
  }

  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.promptForm.get(name) as FormControl;
  }

  getOutPutControl(name: string): FormControl {
    return this.promptsOutputForm.get(name) as FormControl;
  }

  // Handle file attachment for the prompt task
  handleAttachment(): void {
    // Implement file attachment logic here
  }

  // Analyze the prompt task
  analyzePrompt(): void {
    // Implement prompt analysis logic here
  }

  // Load prompt data from mock data
  loadPromptData(promptId: string): void {
    // In a real app, this would use data fetched from a service
    const prompt = MOCK_PROMPTS?.find((p) => p.id === promptId);

    if (prompt) {
      // Set form values based on the prompt data
      this.promptForm.get('name')?.setValue(prompt.title);
      this.promptForm
        .get('description')
        ?.setValue(`This is the ${prompt.title} description.`);

      // Set filter data based on the prompt properties
      this.promptForm.get('organization')?.setValue('Ascendion');
      this.promptForm
        .get('domain')
        ?.setValue(prompt.department || 'AI Research');
      this.promptForm
        .get('project')
        ?.setValue(prompt.project || 'Prompt Engineering');
      this.promptForm.get('team')?.setValue('NLP Team');

      // Set sample values for other fields
      this.promptForm
        .get('promptTask')
        ?.setValue('Generate a conversation about AI safety');
      this.promptForm.get('role')?.setValue('AI Assistant');
      this.promptForm
        .get('goal')
        ?.setValue('Help users understand AI safety concepts');
      this.promptForm
        .get('backstory')
        ?.setValue('You are an expert in AI safety and ethics');
      this.promptForm
        .get('expectedOutput')
        ?.setValue('A clear, informative conversation about AI safety');
    }
  }

  // Handle chat messages
  handleChatMessage(message: string): void {
    if (this.promptId && this.isExecuteMode) {
      this.isProcessingChat = true;

      this.chatMessages.push({
        from: 'user',
        text: message,
      });
      // this.previewPrompt();
      // Process through the service - it will handle adding user and AI messages
      // this.toolExecutionService.processUserMessage(message);

      // Reset loading state after a delay that matches the service's response time
      setTimeout(() => {
        this.isProcessingChat = false;
      }, 1000);
    }
  }
  get examples(): FormArray {
    return this.promptForm.get('examples') as FormArray;
  }

  addExample(): void {
    this.examples.push(
      this.fb.group({
        id: [null],
        input: [''],
        output: [''],
      }),
    );
  }

  removeExample(index: number): void {
    // Only remove if it's not the first one
    if (index > 0) {
      this.examples.removeAt(index);
    }
  }
  //   private previewPrompt(): void {
  //   const payload = {
  //     role: this.getControl('role').value,
  //     goal: this.getControl('goal').value,
  //     backstory: this.getControl('backstory').value,
  //     description: this.getControl('description').value,
  //     expectedOutput: this.getControl('expectedOutput').value
  //   };

  //   this.promptsService.previewPrompt(payload).subscribe({
  //     next: (response) => {
  //       console.log('Preview API Response:', response);

  //       const previewResponseText = typeof response === 'string'
  //         ? response
  //         : JSON.stringify(response, null, 2); // fallback for object

  //       // Add preview as AI message to chat
  //       this.chatMessages.push({
  //         from: 'ai',
  //         text: previewResponseText
  //       });

  //       this.isProcessingChat = false;
  //     },
  //     error: (err) => {
  //       console.error('Error during preview prompt:', err);
  //       this.chatMessages.push({
  //         from: 'ai',
  //         text: 'An error occurred while generating the preview.'
  //       });
  //       this.isProcessingChat = false;
  //     }
  //   });
  // }

  regenerate() {
    const previousPrompt = this.promptCache[this.selectedTab];
    if (previousPrompt) {
      this.prompt.setValue(previousPrompt);
      this.toggleAskAvaModal();
      this.onClickGenerate(previousPrompt);
    }
  }
  ngAfterViewChecked(): void {
    if (!this.accordionRef) return;

    const currentExpanded = this.accordionRef.expanded;

    if (this.previousAccordionState && !currentExpanded) {
      setTimeout(() => {
        this.clearExtraExamples();
      });
    }

    this.previousAccordionState = currentExpanded;
  }

  clearExtraExamples(): void {
    const examplesArray = this.promptForm.get('examples') as FormArray;
    while (examplesArray.length > 1) {
      examplesArray.removeAt(1);
    }
    examplesArray.at(0).get('input')?.reset();
    examplesArray.at(0).get('output')?.reset();
  }

  // Get error message for a specific form field
  getFieldError(fieldName: string): string {
    const field = this.promptForm.get(fieldName);
    // Custom label mapping
    const customLabels: Record<string, string> = {
      name: 'Prompt Name',
      description: 'Description',
      promptTask: 'Freeform Prompt',
      role: 'Role',
      goal: 'Goal',
      promptDescription: 'Description',
      backstory: 'Backstory',
      expectedOutput: 'ExpectedOutput',
      promptType: 'PromptType'
    };

    const formattedFieldName = customLabels[fieldName] || fieldName;

    if (field && field.invalid && (field.touched || field.dirty)) {
      if (field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;
      }
    } else if (fieldName == 'modelType' || fieldName == 'apiVersion') {
      if (field && field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
    }

    return '';
  }

  isSaveDisabled(): boolean {
    if (!this.formChanged) {
      return true;
    }

    const getInvalid = (controlName: string): boolean =>
      this.promptForm.get(controlName)?.invalid ?? false;

    if (this.selectedTab === 'freeform') {
      return (
        getInvalid('name') ||
        getInvalid('promptDescription') ||
        getInvalid('promptTask')
      );
    } else if (this.selectedTab === 'template') {
      return (
        getInvalid('name') ||
        getInvalid('promptDescription') ||
        getInvalid('role') ||
        getInvalid('goal') ||
        getInvalid('backstory') ||
        getInvalid('description') ||
        getInvalid('expectedOutput') ||
        getInvalid('promptType') ||
        (this.promptForm.value.promptType === 'Chain of Thought'
          ? getInvalid('intermediateSteps')
          : false)
      );
    }

    return false;
  }

  // Handle success popup confirmation
  onSuccessConfirm(): void {
    this.closeSuccessPopup();
  }

  // Hide success popup and navigate if successful
  closeSuccessPopup(): void {
    this.showSuccessPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
    if (this.submissionSuccess) {
      this.router.navigate(['/libraries/prompts']);
    }
  }

  toggleAskAvaModal(show = true) {
    this.showAskAvaModal = show;
  }

  onClickGenerate(prompt = this.prompt.value) {
    const isTabFreeForm = this.selectedTab === 'freeform';
    const mode = isTabFreeForm
      ? PromptsModes.useCaseCreation
      : PromptsModes.agnetJsonCreation;
    const useCaseIdentifier = mode + USE_CASE_IDENTIFIER;
    this.isLoading = true;
    this.promptGenerateService
      .modelApi(prompt, mode, false, useCaseIdentifier)
      .subscribe({
        next: (res) => {
          if (isTabFreeForm) {
            this.promptsOutputForm.patchValue({
              promptTask: res?.response?.choices[0]?.text,
            });

            console.log(this.promptsOutputForm);
          } else {
            let generatedPrompt: any = {};
            try {
              generatedPrompt = JSON.parse(res?.response?.choices[0]?.text);
            } catch (error) {
              return;
            }
            const { ROLE, GOAL, BACKSTORY, DESCRIPTION, OUTPUT } =
              generatedPrompt;
            this.promptsOutputForm.patchValue({
              role: ROLE,
              goal: GOAL,
              backstory: BACKSTORY,
              description: DESCRIPTION,
              expectedOutput: OUTPUT,
            });
          }

          this.showPromptOutput = true;
          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
        },
      });
  }

  onUse(): void {
    const outputFormValue = this.promptsOutputForm.getRawValue();
    //  Cache prompt value (if used for tabs)
    this.promptCache[this.selectedTab] = this.prompt.value || '';

    //  Save current name and description before patching
    const { name, promptDescription } = this.promptForm.getRawValue();

    //  Patch all fields from output form
    this.promptForm.patchValue({
      ...outputFormValue,
      name,           // restore preserved values
      promptDescription
    });

    // ✅ Reset child form only (does NOT touch parent)
    this.promptsOutputForm.reset();
    this.prompt.reset('');

    this.showPromptOutput = false;
    this.toggleAskAvaModal(false);
  }

  onReset() {
    this.promptsOutputForm.reset();
    this.showPromptOutput = false;
  }

  onCancle() {
    this.prompt.reset('');
    this.onReset();
    this.toggleAskAvaModal(false);
  }

  onClose() {
    this.toggleAskAvaModal(false);
    this.onCancle();
  }
  onCancel(): void {
    this.router.navigate(['/libraries/prompts']);
  }

  get actionButtonLabel(): string {
    return this.isEditMode ? 'Update' : 'Save';
  }
}
