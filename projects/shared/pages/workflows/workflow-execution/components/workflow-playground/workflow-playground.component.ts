import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IconComponent } from '@ava/play-comp-library';
import { AgentStepperCardComponent } from '../agent-stepper-card/agent-stepper-card.component';

export interface AgentData {
  id: number;
  name: string;
  role: string;
  goal: string;
  backstory: string;
  task: {
    description: string;
    expectedOutput: string;
  };
  serial: number;
  inputs?: AgentInput[];
  hasInputs?: boolean;
}

export interface AgentInput {
  placeholder: string;
  inputName: string;
  inputType: 'text' | 'image';
  value?: string;
  files?: File[];
}

@Component({
  selector: 'app-workflow-playground',
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    AgentStepperCardComponent
  ],
  templateUrl: './workflow-playground.component.html',
  styleUrls: ['./workflow-playground.component.scss']
})
export class WorkflowPlaygroundComponent {
  @Input() agents: AgentData[] = [];
  @Input() isCollapsed: boolean = false;
  @Input() workflowName: string = 'Workflow';

  @Output() backClicked = new EventEmitter<void>();
  @Output() collapseToggled = new EventEmitter<boolean>();
  @Output() agentInputChanged = new EventEmitter<{agentId: number, inputIndex: number, value: string}>();
  @Output() agentFileSelected = new EventEmitter<{agentId: number, inputIndex: number, files: File[]}>();
  @Output() messageSent = new EventEmitter<{agentId: number, inputIndex: number, value: string, files?: File[]}>();

  currentActiveStep: number = 0;
  completedSteps: Set<number> = new Set();

  onBackClick(): void {
    this.backClicked.emit();
  }

  onCollapseToggle(): void {
    this.isCollapsed = !this.isCollapsed;
    this.collapseToggled.emit(this.isCollapsed);
  }

  onAgentInputChange(agentId: number, inputIndex: number, value: string): void {
    this.agentInputChanged.emit({ agentId, inputIndex, value });
  }

  onAgentFileSelect(agentId: number, inputIndex: number, files: File[]): void {
    this.agentFileSelected.emit({ agentId, inputIndex, files });
  }

  onMessageSent(agentIndex: number, event: {inputIndex: number, value: string, files?: File[]}): void {
    const agent = this.agents[agentIndex];
    if (agent) {
      this.messageSent.emit({
        agentId: agent.id,
        inputIndex: event.inputIndex,
        value: event.value,
        files: event.files
      });
    }
  }

  onStepCompleted(agentIndex: number): void {
    this.completedSteps.add(agentIndex);

    // Move to next step if available
    if (agentIndex === this.currentActiveStep && agentIndex < this.agents.length - 1) {
      this.currentActiveStep = agentIndex + 1;
    }
  }

  isStepActive(index: number): boolean {
    return index === this.currentActiveStep;
  }

  isStepCompleted(index: number): boolean {
    return this.completedSteps.has(index);
  }

  trackByAgentId(index: number, agent: AgentData): number {
    return agent.id;
  }
}
