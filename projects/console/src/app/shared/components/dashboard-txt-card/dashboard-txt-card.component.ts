import { Component, Input } from '@angular/core';
import { TxtCardComponent, CardContentComponent, IconComponent } from "@ava/play-comp-library";

@Component({
  selector: 'app-dashboard-txt-card',
  imports: [TxtCardComponent, CardContentComponent, IconComponent],
  templateUrl: './dashboard-txt-card.component.html',
  styleUrl: './dashboard-txt-card.component.scss'
})
export class DashboardTxtCardComponent {
  @Input() title = ""
  @Input() value = 0
  @Input() subtitle = ""


}
