::ng-deep #dashboard-txt-card{
    .dashboard-txt-card-1 {
    .ava-text-card-wrapper {
        .ava-default-card-container {
            .ava-default-card.default-card.card {
                border: none;
                box-shadow: none;
                height: 198px;
                width: 289px;
                padding: 24px;
                background-color: #ffffff;

                ava-card-content {

                    .title-wrapper {
                        display: flex;
                        align-items: center;
                        column-gap: 20px;

                        h1 {
                            margin: 0px;
                            font-family: Mulish;
                            font-weight: 700;
                            font-style: Bold;
                            font-size: 20px;
                            color: #3B3F46;
                        }

                    }

                    .value-wrapper {
                        display: flex;
                        flex-direction: column;
                        row-gap: 10px;

                        h1 {
                            margin: 0px;
                            font-family: Inter;
                            font-weight: 500;
                            font-style: Medium;
                            font-size: 48px;
                            color: #3B3F46;
                        }

                        p {
                            margin: 0px;
                            font-family: Mulish;
                            font-weight: 400;
                            font-style: Regular;
                            font-size: 16px;
                            color: #3B3F46;
                        }
                    }
                }

            }
        }
    }





}
}