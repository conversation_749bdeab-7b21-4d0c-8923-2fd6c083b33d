::ng-deep #dashboard-img-card {
  .dashboard-img-card {
    width: 100%;
    padding: 0rem;

    .ava-image-card-wrapper {
      width: 100%;

      .ava-default-card-container .ava-default-card {
        padding: 1rem;
        width: 100%;
        border: none !important;
        box-shadow: none;

        ava-card-content {
          img {
            width: 220px;
            height: 265px;
          }

          .right-container {
            margin: 1.5rem;
            row-gap: 1.5rem;

            .txt-wrapper {
              margin-top: 3.5625rem;
              display: flex;
              flex-direction: column;
              row-gap: 1.5rem;


              .name {
                font-family: Mulish;
                font-weight: 500;
                font-size: clamp(1.5rem, 2vw, 2rem);
                font-style: Medium;
                line-height: 100%;
                color: #3B3F46;
              }

              .desc {
                font-family: Mulish;
                font-weight: 400;
                font-style: Regular;
                font-size: clamp(2.5rem, 4vw, 3rem);
                color: #3B3F46;
              }
            }

            .tags {

              ava-tag {
                .ava-tag.ava-tag--filled {
                  background: transparent;
                  border-color: 1px solid #BBBEC5;
                  height: 2rem;
                  border-radius: 1.25rem;
                  border-width: 1px;
                  padding-right: 0.75rem;
                  padding-left: 0.75rem;

                  .ava-tag__label {
                    font-family: Mulish;
                    font-weight: 600;
                    font-size: 20px;
                    color: #3B3F46;
                    opacity: 1;
                  }
                }
              }
            }

            .tags,
            .left-wrapper,
            .right-wrapper {
              display: flex;
              height: 2rem;
            }

            .tags,
            .right-wrapper {
              column-gap: 1.5rem;
            }

            .left-wrapper {
              column-gap: 0.5rem;
            }
          }
        }
      }
    }

    .ava-image-card-wrapper {
      transform: scaleX(1);
      transition: transform 0.4s ease;

      .ellips {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }


      &:hover {
        transform: scaleX(1.02) scaleY(1.02)
      }

      ava-card-header {
        h1 {
          margin-top: 1rem;
        }

        .ava-header {
          img {
            border-radius: 12px;
          }
        }
      }

      ava-card-header,
      ava-card-footer {
        .right {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: 10px;
        }

        .left {
          display: flex;
          gap: 10px;
        }
      }

      ava-card-content {
        .image-wrapper {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        p {
          flex: 1;
        }

        img {
          width: 150px;
          object-fit: cover;
          border-radius: 12px;
        }
      }

    }

  }
}