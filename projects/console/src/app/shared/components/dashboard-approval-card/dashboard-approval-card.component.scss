::ng-deep #dashboard-approval-card{
    .ava-console-approval-card-container {
    display: flex;
    flex-direction: column;
    row-gap: 24px;

    .ava-default-card-container {
        .ava-default-card{
            box-shadow: none;
        }
        .header {
            display: flex;
            column-gap: 16px;
            align-items: flex-start;
            margin-bottom: 24px;
            justify-content: space-between;

            div {
                display: flex;
                column-gap: 10px;
                align-items: center;
                        
            }

            h2 {
                font-family: Mulish;
                font-weight: 600;
                font-style: SemiBold;
                font-size: 20px;
                color: #3B3F46;
                width: inherit;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                display: inline-block;
            }

            div {
                .ava-tag {
                    width: 56px;
                    height: 32px;
                    border-radius: 20px;
                    background-color: #DC2626;
                    display: flex;
                    justify-content: center;

                    .ava-tag__label {
                        font-family: Mulish;
                        font-weight: 600;
                        font-style: SemiBold;
                        font-size: 14px;
                        color: #FFFFFF;
                    }
                }

            }
        }

        .content {
            margin-bottom: 24px;

            .tag-wrapper {
                margin-bottom: 24px;
                display: flex;

                gap: 10px;

                .avtar-tag-wrapper {
                    background: #F0F1F2;
                    border: none;
                    border-radius: 4px;
                    height: 32px;
                    display: inline-flex;
                    width: fit-content;
                    align-items: center;
                    padding: 4px 8px;

                    img {
                        height: 24px;
                        width: 24px;
                        border-radius: 50%;
                        object-fit: cover;
                    }

                    .ava-tag {
                        border: none;
                        background: transparent;
                    }

                }

                .ava-tag {
                    background: #F0F1F2;
                    border: none;
                    border-radius: 4px;
                    height: 32px;

                    .ava-tag__label {
                        font-family: Mulish;
                        font-weight: 400;
                        font-style: Regular;
                        font-size: 14px;
                        color: #616874;
                    }
                }

                .green-tag {
                    .ava-tag {
                        background: #C5EBDD;
                        border: none;
                        border-radius: 4px;
                        height: 32px;

                        .ava-tag__label {
                            font-family: Mulish;
                            font-weight: 700;
                            font-style: Bold;
                            font-size: 12px;
                            color: #33364D;
                            text-transform: capitalize;
                        }
                    }

                }
            }

            .info-wrapper {
                display: flex;
                justify-content: space-between;

                .email,
                .date {
                    font-family: Mulish;
                    font-weight: 400;
                    font-style: Regular;
                    font-size: 14px;
                    color: #3B3F46;
                    display: flex;
                    gap: 6px;

                }


            }
        }

        .footer {
            display: flex;
            justify-content: flex-end;
            gap: 8px;


            .grandient-border-btn {

                display: flex;
                gap: 8px;

                .grandient-border {
                    background: linear-gradient(130.87deg, #0084FF 33.91%, #03BDD4 100%);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 15px;
                }

                .ava-button {
                    border: none;
                    background: #FFFFFF;
                    color: transparent;
                    box-shadow: none;

                    svg {
                        stroke: #0084FF;
                    }

                    .button-label {
                        background: linear-gradient(130.87deg, #0084FF 33.91%, #03BDD4 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        color: transparent;
                        font-family: Mulish;
                        font-weight: 500;
                        font-size: 14px;
                    }
                }
            }

            .approval-btn {
                .button-label {
                    font-family: Mulish;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                }
            }
        }
    }
}
}