#app-container {
  width: 100%;
  height: 100vh;
  padding: 0;
  overflow: hidden;
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  position: relative;
  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    pointer-events: none;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.02), transparent);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &:hover::before {
    opacity: 1;
  }
}

.app-container.login-active {
  padding: 0 !important;
}

::ng-deep app-nav-header {
  position: relative !important;
}

.retractable-header {
  width: 100%;
  padding: 0px 0px 0px 0px;
  &.hidden {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.02),
      0 1px 3px rgba(0, 0, 0, 0.01);
    margin-bottom: 0;
  }
}

.retractable-breadcrumb {
  width: 100%;
  padding: 0 2rem;
  &.hidden {
    max-height: 0;
    opacity: 0;
    transform: translateY(-5px);
    margin-bottom: 0;
  }
}

.post-login {
  padding: 0 1rem;
  margin-top: 2rem;
}

.content-wrapper {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  transform: translateY(0);
}

.content-wrapper::-webkit-scrollbar {
  display: none;
}

::ng-deep router-outlet + * {
  position: relative;
}

footer {
  padding:1rem;
  text-align:center;
  bottom:0;
}
