import { Component, OnInit } from '@angular/core';
import { CommonModule, formatDate } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Router } from '@angular/router';
import { SharedApiServiceService } from '../../shared/services/shared-api-service.service';
import {
  AvaTagComponent,
  DropdownComponent,
  DropdownOption,
  LinkComponent,
} from '@ava/play-comp-library';
import {
  ActivityMonitoringI,
  DashboardDetailI,
} from './models/dashboard.model';
import { ApprovalService } from '../../shared/services/approval.service';
import {
  ACTIVE_MONITORING_OPTIONS,
  ActiveMonitorings,
  APIKeys,
  DASHBOARD_CARD_DETAILS,
} from './constants/dashoard.constant';
import { RequestStatus } from '../approval/approval.component';
import approvalText from '../approval/constants/approval.json';
import { DashboardImgCardComponent } from '../../shared/components/dashboard-img-card/dashboard-img-card.component';
import { DashboardTxtCardComponent } from '../../shared/components/dashboard-txt-card/dashboard-txt-card.component';
import { DashboardAgentMonitoringCardComponent } from '../../shared/components/dashboard-agent-monitoring-card/dashboard-agent-monitoring-card.component';
import { DashboardApprovalCardComponent } from '../../shared/components/dashboard-approval-card/dashboard-approval-card.component';

// Interfaces
interface UserLog {
  id: string;
  username: string;
  avatar: string;
  securityToken: string;
  status: 'Active' | 'Inactive' | 'Pending';
}

interface ModelUsage {
  id: string;
  name: string;
  publisher: {
    name: string;
    logo: string;
  };
  agentsCount: number;
}

interface PendingApproval {
  id: string;
  name: string;
  type: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    DropdownComponent,
    AvaTagComponent,
    LinkComponent,
    DashboardImgCardComponent,
    DashboardTxtCardComponent,
    DashboardAgentMonitoringCardComponent,
    DashboardApprovalCardComponent,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {
  options: DropdownOption[] = ACTIVE_MONITORING_OPTIONS;
  public labels: any = approvalText.labels;

  dashboardDetails: DashboardDetailI[] = [
    // {
    //   icon: '',
    //   title: '',
    //   value: 0,
    //   subtitle: '',
    //   badge: '',
    // },
    // {
    //   icon: 'trending-up',
    //   title: 'Active Workflows',
    //   value: 80,
    //   subtitle: 'Agents actively running',
    //   badge: 'S',
    // },
    // {
    //   icon: 'trending-up',
    //   title: 'Active Agents',
    //   value: 1240,
    //   subtitle: 'Agents active',
    //   badge: 'S',
    // },
    // {
    //   icon: 'trending-up',
    //   title: 'Agents Approval',
    //   value: 120,
    //   subtitle: 'Agents actively running',
    //   badge: 'S',
    // },
    // {
    //   icon: 'trending-up',
    //   title: 'Active Workflows',
    //   value: 80,
    //   subtitle: 'Agents actively running',
    //   badge: 'S',
    // },
  ];
  // Dashboard metrics
  totalAgents: number = 0;
  newAgentsCreated: number = 50;

  totalWorkflows: number = 124;
  newWorkflowsCreated: number = 50;

  totalUsers: number = 300;
  newUsersAdded: number = 50;

  selectedActiveMonitoring = ActiveMonitorings.tools;

  activityMonitoringCount = 5;

  // Current user info (would come from auth service in real app)
  currentUser: { name: string } = { name: 'Akash Raj' };

  // Footer info
  currentYear: number = new Date().getFullYear();

  // Model usage data
  modelUsage: ModelUsage[] = [
    {
      id: '1',
      name: 'GPT 3',
      publisher: {
        name: 'Open AI',
        logo: 'assets/images/logos/openai-logo.png',
      },
      agentsCount: 48,
    },
    {
      id: '2',
      name: 'Claude 2',
      publisher: {
        name: 'Anthropic',
        logo: 'assets/images/logos/anthropic-logo.png',
      },
      agentsCount: 24,
    },
    {
      id: '3',
      name: 'Gemini',
      publisher: {
        name: 'Google',
        logo: 'assets/images/logos/google-logo.png',
      },
      agentsCount: 20,
    },
    {
      id: '4',
      name: 'GPT-4',
      publisher: {
        name: 'Open AI',
        logo: 'assets/images/logos/openai-logo.png',
      },
      agentsCount: 8,
    },
  ];

  // Pending approvals
  pendingApprovals: PendingApproval[] = [
    {
      id: '1',
      name: 'Test Ruby to Springboot',
      type: 'migration',
    },
    {
      id: '2',
      name: 'Customer Support Chatbot',
      type: 'agent',
    },
    {
      id: '3',
      name: 'Invoice Processing & Approval',
      type: 'workflow',
    },
    {
      id: '4',
      name: 'Invoice Processing & Approval',
      type: 'workflow',
    },
    {
      id: '5',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
    {
      id: '6',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
    {
      id: '7',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
    {
      id: '8',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
  ];

  activityMonitoring: ActivityMonitoringI[] = [
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
  ];
  workflowApprovals: any[] = [];
  approvalTabs: string[] = ['Agents', 'Workflow', 'Tools'];
  selectedApprovalTab: string = 'Agents';
  approvalData: any[] = []

  constructor(
    private router: Router,
    private apiService: SharedApiServiceService,
    private approvalService: ApprovalService,
  ) { }

  // seprating the tool and agents.
  activityMonitoringTools: ActivityMonitoringI[] = [];
  activityMonitoringAgents: ActivityMonitoringI[] = [];

  setActiveMonitoringData() {
    // INsted of re-formating, just switch the values
    if (this.selectedActiveMonitoring === ActiveMonitorings.tools) {
      this.activityMonitoring = this.activityMonitoringTools;
    } else {
      this.activityMonitoring = this.activityMonitoringAgents;
    }
  }

  mapToActivityMonitoringItem = (
    item: any,
    nameKey: APIKeys,
    usageKey: APIKeys,
  ): ActivityMonitoringI => ({
    agentName: item[nameKey] || '',
    totalRuns: Number(item[usageKey]) || 0,
    status: '',
    user: '',
    date: '',
  });

  public toRequestStatus(value: string | null | undefined): RequestStatus {
    return value === 'approved' || value === 'rejected' || value === 'review'
      ? value
      : 'review';
  }

  initApiCalls() {
    const date = new Date();
    const dateEnd = this.apiService.formatDate(date);
    date.setDate(1);
    const dateStart = this.apiService.formatDate(date);
    this.apiService
      .getCollabrativeAgentAnalytics(dateStart, dateEnd)
      .subscribe((response: Record<string, any>) => {
        // setting dashboard card values
        this.dashboardDetails = DASHBOARD_CARD_DETAILS.map((cardDetail) => {
          cardDetail.value =
            (response[cardDetail.field] as number) || this.totalAgents;
          return cardDetail as DashboardDetailI;
        });

        // Active Monitoring
        // Extracting tools and agents to seprate varibales to reduce frequent re-formatig as the dropdown value changes
        this.activityMonitoringTools = (response[APIKeys.toolUsage] as any[])
          .slice(0, this.activityMonitoringCount)
          .map((toolUsage) =>
            this.mapToActivityMonitoringItem(
              toolUsage,
              APIKeys.toolName,
              APIKeys.usageCount,
            ),
          );

        this.activityMonitoringAgents = (
          response[APIKeys.agentMetrics] as any[]
        )
          .slice(0, this.activityMonitoringCount)
          .map((agentMetric) =>
            this.mapToActivityMonitoringItem(
              agentMetric,
              APIKeys.agentName,
              APIKeys.workflowCount,
            ),
          );

        this.setActiveMonitoringData();
      });

    this.approvalService.getAllReviewAgents(1, 100, false).subscribe((res) => {
      const agentReviewDetails = res?.agentReviewDetails || [];
      // Extracting agents which are under review
      this.totalAgents =
        agentReviewDetails.filter(
          (agentReviewDetail: any) => agentReviewDetail.status !== 'approved',
        )?.length || 0;

      // If this API call is late then will set approval count here.
      const toolCardDetial = this.dashboardDetails.at(-1);
      if (toolCardDetial) {
        toolCardDetial.value = this.totalAgents;
      }
    });

    this.approvalService
      .getAllReviewWorkflows(1, 5, false)
      .subscribe((response) => {
        const type = 'workflow';

        this.workflowApprovals = response.workflowReviewDetails?.map(
          (req: any) => {
            const statusIcons: Record<RequestStatus, string> = {
              approved: 'circle-check-big',
              rejected: 'circle-x',
              review: 'clock',
            };
            const statusTexts: Record<RequestStatus, string> = {
              approved: this.labels.approved,
              rejected: this.labels.rejected,
              review: this.labels.review,
            };
            const statusKey = this.toRequestStatus(req?.status);
            let specificId = 0;
            let title = '';

            specificId = req.workflowId;
            title = req.workflowName;

            return {
              id: req.id,
              refId: specificId,
              type: type,
              session1: {
                title: title,
                labels: [
                  {
                    name: type,
                    color: 'success',
                    background: 'red',
                    type: 'normal',
                  },
                  {
                    name: req.changeRequestType,
                    color:
                      req.changeRequestType === 'update' ? 'error' : 'info',
                    background: 'red',
                    type: 'pill',
                  },
                ],
              },
              session2: [
                {
                  name: type,
                  color: 'default',
                  background: 'red',
                  type: 'normal',
                },
                {
                  name: req.status,
                  color: 'default',
                  background: 'red',
                  type: 'normal',
                },
              ],
              session3: [
                {
                  iconName: 'user',
                  label: req.requestedBy,
                },
                {
                  iconName: 'calendar-days',
                  label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),
                },
              ],
              session4: {
                status: statusTexts[statusKey],
                iconName: statusIcons[statusKey],
              },
            };
          },
        );
      });
  }

  ngOnInit(): void {
    // The layout is now managed with fixed heights in CSS
    // No need for recalculateLayout

    this.initApiCalls();
    this.getAllReviewAgents();
  }

  // Navigate to a route
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  // Test method to demonstrate loader functionality
  testLoader(): void {
    this.apiService.getConfigLabels().subscribe({
      next: (response) => {},
      error: (error) => {},
    });
  }

  uClick(index: any) {
    this.router.navigate(['/approval']);
  }

  onSelectionChange(data: any) {
    this.selectedActiveMonitoring = data.selectedValue;
    this.setActiveMonitoringData();
  }

  onQuickActionClick(action: any) {
    console.log('Quick action clicked:', action);
    // Handle navigation or action based on action.id
    switch (action.id) {
      case 'build-agent':
        this.router.navigate(['/build/agents/create']);
        break;
      case 'build-workflow':
        this.router.navigate(['/build/workflows/create']);
        break;
      case 'create-prompt':
        this.router.navigate(['/libraries/prompts/create']);
        break;
      case 'create-tool':
        this.router.navigate(['/libraries/tools/create']);
        break;
      case 'create-guardrail':
        this.router.navigate(['/libraries/guardrails/create']);
        break;
      case 'create-knowledge-base':
        this.router.navigate(['/libraries/knowledge-base/create']);
        break;
      default:
        console.log('Unknown action:', action.id);
    }
  }

  // Common pill base style
  private readonly basePillStyle: Record<string, string> = {
    'border-radius': '20px',
    padding: '0px 20px',
    height: '32px',
    display: 'flex',
    'justify-content': 'center',
  };

  // Custom pill style for Agent Approvals card
  agentApprovalsPillStyle: Record<string, string> = {
    ...this.basePillStyle,
    'background-color': '#fff',
    color: '#2D3036',
    border: '1px solid #2563EB',
  };

  // Selected state pill style
  agentApprovalsSelectedPillStyle: Record<string, string> = {
    ...this.basePillStyle,
    'background-color': '#2563EB',
    color: '#FFFFFF',
  };

  updatedSelectedApprovalTab(tab: string) {
    this.selectedApprovalTab = tab;
    if (tab == 'Agents') {
      this.getAllReviewAgents();
    } else if (tab == 'Workflow') {
      this.getAllReviewWorkflows();
    } else if (tab == 'Tools') {
      this.getAllReviewTools();
    }
  }

  getAllReviewAgents() {
    this.approvalService.getAllReviewAgents(1, 3, false).subscribe((response) => {
      this.approvalData = (response?.agentReviewDetails || []).map((item: any) => ({
        id: item.id,
        title: item.agentName,
        requestedBy: item.requestedBy,
        requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),
        type: 'agent',
        rawData: item,
      }));
    });
  }

  getAllReviewWorkflows() {
    this.approvalService.getAllReviewWorkflows(1, 3, false).subscribe((response) => {
      this.approvalData = (response?.workflowReviewDetails || []).map((item: any) => ({
        title: item.workflowName,
        requestedBy: item.requestedBy,
        requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),
        type: 'workflow',
        rawData: item,
      }));
    });
  }

  getAllReviewTools() {
    this.approvalService.getAllReviewTools(1, 3, false).subscribe((response) => {
      this.approvalData = (response?.userToolReviewDetails || []).map((item: any) => ({
        title: item.toolName,
        requestedBy: item.requestedBy,
        requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),
        type: 'tool',
        rawData: item,
      }));
    });
  }

  approveItem( item:any) {
    // let approvalCall$;
    // switch (type) {
    //   case 'agent':
    //     approvalCall$ = this.approvalService.approveAgent(item.id, entityId, status, reviewedBy);
    //     break;
    //   case 'workflow':
    //     approvalCall$ = this.approvalService.approveWorkflow(id, entityId, status, reviewedBy);
    //     break;
    //   case 'tool':
    //     approvalCall$ = this.approvalService.approveTool(id, entityId, status, reviewedBy);
    //     break;
    //   default:
    //     console.error(`Unknown approval type: ${type}`);
    //     return;
    // }

    // approvalCall$.subscribe(() => {
    //   console.log(`${type} approved successfully`);
    //   // optionally refresh the data here
    // });
  }

  onTestClick(event: any){}

  onSendBackClick(event: any){}

  goToAnalytics() {
    this.router.navigate(['/console/analytics']);
  }
}
