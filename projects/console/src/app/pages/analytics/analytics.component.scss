.analytics-container {
  padding: 1rem;
}

.analytics-header {
    margin-bottom: 1rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
  }

  .analytics-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0 0 1.5rem 0;
  }
}
.analytics-tabs{
    margin-bottom: 10px;
}


// Loading States
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;

  p {
    margin-top: 1rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
  }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--card-border);
  border-top: 4px solid var(--dashboard-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}



@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Action Bar Styles
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-box-shadow);
}

.analytics-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

// Base styles - consolidated to reduce duplication
%custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.6) transparent;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(139, 92, 246, 0.6);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover {
      background-color: rgba(139, 92, 246, 0.8);
    }
  }
}

%usage-bar-base {
  background: var(--border-color);
  border-radius: 3px;
  overflow: hidden;

  .bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    border-radius: 3px;
    transition: width 0.3s ease;
  }
}

%base-card {
  padding: 1rem;
  background: var(--agent-chat-column-bg, #f9f9f9);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

%section-header {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &::before {
    content: '';
    width: 4px;
    height: 20px;
    border-radius: 2px;
  }
}

%base-analytics-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--nav-hover);
  color: var(--nav-text, #666D99);
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: var(--nav-hover);
    color: var(--nav-item-active-color);

    &:after {
      opacity: 0.1;
    }
  }

  &:after {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  i {
    font-size: 0.9rem;
  }
}

.analytics-btn {
  @extend %base-analytics-btn;
  transform: translateY(0);

  &:hover {
    transform: translateY(-2px);
  }
}

.download-container {
  position: relative;
}

.download-button {
  position: relative;
}

.download-toggle-btn {
  @extend %base-analytics-btn;

  &:hover {
    transform: translateY(-1px);
  }
}

.download-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  width: 35%;

  button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--dashboard-bg-light);
    }

    &:first-child {
      border-radius: 8px 8px 0 0;
    }

    &:last-child {
      border-radius: 0 0 8px 8px;
    }
  }
}

.date-filter-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.date-inputs {
  display: flex;
  gap: 0.5rem;
}

.filter-btn {
  @extend %base-analytics-btn;

  &:hover {
    transform: translateY(-1px);
  }
}

// Filter Section Styles
.filter-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-box-shadow);
}

.filter-dropdown {
  width: 100%;
  max-width: 400px;

  .mat-mdc-form-field-wrapper {
    background: var(--card-bg);
  }

  .mat-mdc-select-value {
    color: var(--text-color);
  }

  .mat-mdc-form-field-outline {
    color: var(--card-border);
  }

  .mat-mdc-form-field-label {
    color: var(--text-secondary);
  }
}

// User Consumption Section with Scrolling
.user-consumption-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 1.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: '';
      width: 4px;
      height: 20px;
      background: var(--primary-color);
      border-radius: 2px;
    }
  }

  .user-consumption-container {
    .user-consumption-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid var(--border-color);

      span {
        font-weight: 600;
        color: var(--text-color);
        font-size: 1rem;
      }

      .export-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.875rem;
        transition: background-color 0.2s ease;

        &:hover {
          background: var(--primary-hover);
        }

        i {
          font-size: 0.75rem;
        }
      }
    }

    .user-consumption-grid {
      max-height: 400px;
      overflow-y: auto;
      padding-right: 8px;

      // Custom scrollbar
      scrollbar-width: thin;
      scrollbar-color: var(--border-color) transparent;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: var(--border-color);
        border-radius: 3px;

        &:hover {
          background-color: var(--text-secondary);
        }
      }

      .user-consumption-card {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--secondary-background);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        margin-bottom: 0.75rem;
        transition: all 0.2s ease;

        &:hover {
          background: var(--hover-background);
          border-color: var(--primary-color);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .user-info {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;

          .user-email {
            font-weight: 500;
            color: var(--text-color);
            font-size: 0.875rem;
            word-break: break-word;
          }

          .consumption-count {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-weight: 600;
          }
        }

        .usage-bar-container {
          display: flex;
          align-items: center;
          gap: 0.75rem;

          .usage-bar {
            @extend %usage-bar-base;
            flex: 1;
            height: 6px;
          }

          .usage-percentage {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--primary-color);
            min-width: 35px;
            text-align: right;
          }
        }
      }
    }
  }
}

.consumption-card {
  @extend %base-card;

  &:hover {
    border-color: var(--dashboard-primary);
  }
}

.user-info {
  margin-bottom: 1rem;

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.25rem 0;
  }

  p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
  }
}

.usage-stats {
  .stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;

    .label {
      font-size: 0.9rem;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .value {
      font-size: 1rem;
      font-weight: 600;
      color: var(--dashboard-primary);
    }
  }
}

.usage-bar {
  @extend %usage-bar-base;
  width: 100%;
  height: 8px;
}

// No Data Message
.no-data-message {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);

  i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  p {
    font-size: 0.9rem;
    margin: 0;
  }
}

// Tool Usage Section
.tool-usage-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-box-shadow);

  h3 {
    @extend %section-header;

    &::before {
      background: var(--success-color);
    }
  }
}

.tool-usage-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.tool-card {
  @extend %base-card;

  &:hover {
    border-color: var(--success-color);
  }
}

.tool-info {
  margin-bottom: 1rem;

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: '🔧';
      font-size: 0.9rem;
    }
  }

  p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
  }
}

// User Activity Stats
.user-activity-stats {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-box-shadow);

  h3 {
    @extend %section-header;

    &::before {
      background: var(--dashboard-accent);
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  @extend %base-card;
  display: flex;
  align-items: center;
  gap: 1rem;

  &:hover {
    border-color: var(--dashboard-accent);
  }
}

.stat-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--dashboard-accent), var(--dashboard-primary));
  border-radius: 50%;
  color: white;

  i {
    font-size: 1.2rem;
  }
}

.stat-info {
  flex: 1;

  h4 {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin: 0 0 0.25rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
  }
}

// Tab Navigation - Using ava-tabs component with input properties
// Styling is now handled via ava-tabs input properties
// This is more efficient and maintainable than ::ng-deep

.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.tab-content {
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-in-out;
}

// Metrics Container - Using ava-text-card components
.metrics-container {
  margin-bottom: 2rem;

  .metrics-row {
    display: flex;
    gap: 1rem;

    // Equal width distribution for all cards
    ava-text-card {
      flex: 1;
      min-width: 0; // Allows flex items to shrink below their content size
    }

    @media (max-width: 1200px) {
      gap: 0.75rem;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;

      ava-text-card {
        flex: none; // Reset flex on mobile
      }
    }
  }
}

// Analytics Controls
.analytics-controls {
  margin-bottom: 24px;

  .controls-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;

    .left-buttons {
      position: relative;
      display: flex;
      gap: 12px;
      align-items: center;

      .download-button {
        position: relative;
      }

      .download-icon {
        cursor: pointer;
        padding: 8px;
        border-radius: 6px;
        transition: background-color 0.2s;
        position: relative;

        &:hover {
          background-color: var(--hover-bg, #f5f5f5);
        }
      }
    }

    .right-calendar {
      min-width: 300px;
      display: flex;
      align-items: center;
      gap: 12px;

      .calendar-container {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .calendar-header {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .calendar-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-color);
          }

          .selected-range {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 11px;
            color: var(--text-secondary);
            padding: 4px 8px;
            background: var(--card-bg);
            border-radius: 4px;
            border: 1px solid var(--card-border);
            white-space: nowrap;

            ava-icon {
              color: var(--primary-color);
            }

            .range-text {
              font-weight: 500;
              color: var(--text-color);
            }
          }
        }
      }

      .filter-button {
        flex-shrink: 0;
        min-width: 44px;
        height: 44px;
        border-radius: 8px;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .left-buttons {
        width: 100%;
        justify-content: flex-start;
      }

      .right-calendar {
        min-width: 100%;
        width: 100%;
      }
    }
  }
}

// Download Dropdown
.left-buttons {
  .download-button {
    .download-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      background: white;
      border: 1px solid var(--card-border, #e5e7eb);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      width: 100%;
      margin-top: 4px;

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      border: none;
      background: none;
      width: 100%;
      text-align: left;
      cursor: pointer;
      transition: background 0.2s;
      font-size: 14px;

      &:first-child {
        border-radius: 8px 8px 0 0;
      }

      &:last-child {
        border-radius: 0 0 8px 8px;
      }

      &:hover {
        background: var(--hover-bg, #f5f5f5);
      }

      ava-icon {
        color: var(--text-secondary, #6b7280);
        flex-shrink: 0;
      }

      span {
        font-size: 14px;
        color: var(--text-color, #374151);
      }
    }
    }
  }
}

// Chart Download Icons
.chart-download-icon {
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: var(--text-secondary, #6b7280);

  &:hover {
    background-color: var(--hover-bg, #f5f5f5);
    color: var(--text-color, #374151);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}


.chart-content1{
    padding: 20px;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 2rem;
}

// Digital Ascender Layout - 2x4 Grid
.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 2rem;

  .chart-container {
    background: var(--agent-chat-column-bg, #f9f9f9);
    border: 1px solid var(--card-border);
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-box-shadow);
    padding: 1.5rem;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    // Ensure charts fill the container properly
    highcharts-chart {
      flex: 1;
      min-height: 320px;
      width: 100% !important;

      .highcharts-container {
        width: 100% !important;
        height: 100% !important;
      }
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid var(--card-border);

      h3 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-color);
        margin: 0;
      }

      .export-btn {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: 0.8rem;
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: var(--dashboard-primary);
        }

        i {
          font-size: 0.7rem;
        }
      }
    }



    // Enhanced styling for user consumption chart
    &:first-child {
      .highcharts-container {
        overflow: visible !important;
      }

      .highcharts-scrollbar {
        opacity: 0.7;
        transition: opacity 0.3s ease;

        &:hover {
          opacity: 1;
        }
      }

      .highcharts-scrolling {
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 8px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #8B7EC8;
          border-radius: 4px;

          &:hover {
            background: #7A6DB8;
          }
        }
      }
    }
  }
}

// Duplicate chart-container removed - using the one in charts-grid

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;

  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
}

.export-btn {
  @extend %base-analytics-btn;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  min-height: 32px;

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(-1px);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 102, 204, 0.3);
  }

  i {
    font-size: 0.8rem;
    transition: transform 0.2s ease;
  }

  &:hover i {
    transform: scale(1.1) rotate(5deg);
  }

  .download-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
    transition: all 0.3s ease;
    opacity: 1;
    display: inline-block;
    vertical-align: middle;
    max-width: 100%;
    max-height: 100%;
    background-color: transparent;
    border: none;
  }

  &:hover .download-icon {
    transform: scale(1.1) rotate(5deg);
    opacity: 0.8;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;

    &:hover {
      transform: none;
    }

    .download-icon {
      opacity: 0.4;
    }
  }
}

.overview-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.chart-placeholder {
  height: 300px;
  background: var(--dashboard-bg-light);
  border: 2px dashed var(--card-border);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  p {
    color: var(--text-secondary);
    font-style: italic;
    margin: 0;
  }
}

.recent-activity {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-border-radius);
  padding: 1.5rem;
  box-shadow: var(--card-box-shadow);

  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 1rem 0;
  }
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--dashboard-bg-light);
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--dashboard-bg-lighter);
  }
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--dashboard-bg-icon-button);
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    font-size: 1rem;
    color: var(--dashboard-primary);
  }
}

.activity-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  .activity-title {
    font-weight: 500;
    color: var(--text-color);
  }

  .activity-time {
    font-size: 0.85rem;
    color: var(--text-secondary);
  }
}

// Reports Section
.reports-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.report-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-border-radius);
  padding: 1.5rem;
  box-shadow: var(--card-box-shadow);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: var(--card-hover-shadow);
  }
}

.report-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;

  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    flex: 1;
  }
}

.download-btn, .generate-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.9;
  }

  i {
    font-size: 0.85rem;
  }
}

.generate-btn {
  background: var(--dashboard-bg-icon-button);
  color: var(--dashboard-primary);
  border: 1px solid var(--card-border);

  &:hover {
    background: var(--dashboard-bg-light);
  }
}

.report-content {
  p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
  }
}

.report-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;

  .stat {
    font-size: 0.85rem;
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    background: var(--dashboard-bg-light);
    border-radius: 4px;
    border: 1px solid var(--card-border);
  }
}

// Agents Table
.agents-table {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-border-radius);
  padding: 1.5rem;
  box-shadow: var(--card-box-shadow);
  margin-top: 2rem;

  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 1rem 0;
  }
}

.table-container {
  overflow-x: auto;
}

.agents-data-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--card-border);
  }

  th {
    background: var(--dashboard-bg-light);
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  td {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }

  tr:hover {
    background: var(--dashboard-bg-light);
  }
}

.performance-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.high {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  &.medium {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.2);
  }

  &.low {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .analytics-container {
    padding: 1rem;
  }

  .analytics-header {
    h1 {
      font-size: 2rem;
    }

    .analytics-subtitle {
      margin-bottom: 1rem;
    }
  }

  .action-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .analytics-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .analytics-btn {
    justify-content: center;
    padding: 1rem;
  }

  .date-filter-container {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .date-inputs {
    flex-direction: column;
    gap: 0.5rem;
  }

  .filter-btn {
    justify-content: center;
  }


  .metrics-row {
    flex-direction: column;
    gap: 1rem;
    overflow-y: auto;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .charts-section {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .consumption-grid {
    grid-template-columns: 1fr;
  }

  .tool-usage-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }



  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .export-btn {
    align-self: flex-end;
  }

  .filter-dropdown {
    max-width: 100%;
  }

  .download-dropdown {
    right: auto;
    left: 0;
    width: 100%;
  }

  .consumption-card,
  .tool-card,
  .stat-card,
  .user-consumption-section,
  .tool-usage-section,
  .user-activity-stats {
    padding: 1rem;
    margin: 1rem 0;
  }

  .agents-data-table {
    font-size: 0.8rem;

    th, td {
      padding: 0.5rem;
    }
  }

  .table-container {
    overflow-x: scroll;
  }

  .report-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .report-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
}

// Dark theme adjustments - consolidated
@media (prefers-color-scheme: dark) {
  .analytics-btn,
  .download-dropdown {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

    &:hover {
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }
  }
}

// Simple Table Styling for Collaborative Agent Metrics
.chart-container {
  .agent-metrics-table-container {
    height: 350px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--card-background);

    .table-wrapper {
      height: 100%;
      overflow-y: auto;

      @extend %custom-scrollbar;
    }

    .agent-metrics-table {
      width: 100%;
      border-collapse: collapse;
      font-family: 'Mulish', sans-serif;

      thead {
        position: sticky;
        top: 0;
        z-index: 10;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef); // Professional gradient background

        th {
          padding: 12px 16px;
          text-align: left;
          font-weight: 600;
          font-size: 14px;
          color: #495057; // Darker text for better contrast
          border-bottom: 2px solid var(--dashboard-primary);
          background: linear-gradient(135deg, #f8f9fa, #e9ecef); // Consistent with thead
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); // Subtle shadow for depth

          &.expand-column {
            width: 50px;
            padding: 12px 8px;
            text-align: center;
          }

          // Add hover effect for interactive feel
          &:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
          }
        }
      }

      tbody {
        .agent-row {
          border-bottom: 1px solid var(--border-color);
          transition: background-color 0.2s ease;

          &:hover {
            background-color: var(--hover-background);
          }

          td {
            padding: 12px 16px;
            font-size: 14px;
            color: var(--text-color);
            vertical-align: middle;

            &.expand-column {
              width: 50px;
              padding: 12px 8px;
              text-align: center;
              vertical-align: middle;
            }

            &.agent-name {
              font-weight: 500;
            }

            &.workflow-count {
              font-weight: 600;
              color: var(--primary-color);
            }
          }
        }

        .workflow-details-row {
          background-color: var(--secondary-background);

          td {
            padding: 0;
            border-bottom: 1px solid var(--border-color);
          }

          .workflow-details {
            padding: 16px 24px;

            .workflow-header {
              font-weight: 600;
              font-size: 13px;
              color: var(--text-color);
              margin-bottom: 8px;
            }

            .workflow-list {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .workflow-item {
                padding: 4px 8px;
                background-color: var(--card-background);
                border-radius: 4px;
                font-size: 13px;
                color: var(--text-secondary);
                border-left: 3px solid var(--primary-color);
              }
            }
          }
        }
      }
    }

    .expand-btn {
      background: var(--card-bg);
      border: 1px solid var(--card-border);
      cursor: pointer;
      padding: 6px;
      border-radius: 6px;
      color: var(--text-color);
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;

      &:hover {
        background-color: var(--nav-hover);
        border-color: var(--dashboard-primary);
        color: var(--dashboard-primary);
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }

      svg {
        width: 16px;
        height: 16px;
        transition: transform 0.2s ease;
      }

      &.expanded svg {
        transform: rotate(180deg);
      }
    }
  }
}

// Custom Bar Charts - Updated to match chart-container styling
// Remove the old user-consumption and tool-usage styles since we're using chart-container now

.bars-container {
  flex: 1;
  max-height: 250px; // Adjusted to fit within chart-container
  min-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  margin-top: 8px;

  @extend %custom-scrollbar;

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 8px !important;
    background: rgba(0, 0, 0, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.7) !important;
    border-radius: 4px;

    &:hover {
      background: rgba(139, 92, 246, 0.9) !important;
    }
  }
}

.bar-container {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
  padding: 2px 0;
  min-height: 50px; // Ensure minimum height per bar
}

.bar-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.bar {
  background: linear-gradient(135deg, #8B5CF6, #A855F7); // Professional purple gradient for user consumption
  height: 3rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: visible;
  min-width: 150px; // Increased minimum width for better visibility
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25);
  transition: all 0.3s ease;
  margin-bottom: 2px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(139, 92, 246, 0.35);
  }

  &.tool-bar {
    background: linear-gradient(135deg, #F97316, #FB923C); // Professional orange gradient for tool usage
    box-shadow: 0 2px 6px rgba(249, 115, 22, 0.25);

    &:hover {
      box-shadow: 0 6px 12px rgba(249, 115, 22, 0.35);
    }
  }
}

.bar-label {
  color: #000;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  position: absolute;
  left: 8px;
  right: 8px;
  overflow: visible;
  z-index: 1;
  max-width: calc(100% - 16px);
}

.bar-value {
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  width: 50px;
  text-align: right;
  margin-left: 10px;
  color: var(--text-color);
}

.no-data-bar {
  color: var(--text-secondary);
  font-size: 12px;
  font-style: italic;
}

.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 15rem;
  color: var(--text-secondary);

  .error-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;

    .no-data-icon {
      font-size: 2rem;
      color: var(--text-secondary);
    }

    .no-data {
      font-size: 1rem;
      font-weight: 500;
    }
  }
}

// Responsive design for custom bar charts
@media (max-width: 768px) {
  .user-consumption, .tool-usage {
    width: 100%;
    height: auto;
    min-height: 20rem;
  }

  .user-consumption-container, .tool-usage-container {
    padding: 15px;
  }

  .bars-container {
    max-height: 15rem;
  }

  .bar {
    height: 2rem;
  }

  .bar-label {
    font-size: 11px;
    left: 6px;
  }

  .bar-value {
    font-size: 11px;
    width: 40px;
  }

  .user-consumption-header, .tool-usage-header {
    font-size: 16px;
  }
}

// Gradient Divider
.gradient-divider {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #E30A6D 50%, transparent 100%);
  margin: 0 16px;
  border-radius: 1px;
}
