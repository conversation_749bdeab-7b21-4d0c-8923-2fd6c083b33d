import { CommonModule, formatDate } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import {
  ApprovalCardComponent,
  IconComponent,
  AvaTextboxComponent,
  TextCardComponent,
  PopupComponent,
  ConfirmationPopupComponent,
  AvaTagComponent,
  DropdownOption,
  ButtonComponent,
  DialogService,
  CubicalLoadingComponent,
  DialogButton,
} from '@ava/play-comp-library';
import approvalText from '../constants/approval.json';
import { SharedApiServiceService } from '../../../shared/services/shared-api-service.service';
import { ApprovalService } from '../../../shared/services/approval.service';
import { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';
import { DrawerService } from '../../../shared/services/drawer/drawer.service';
import { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';
import { AgentServiceService } from '@shared/index';
import { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';

type RequestStatus = 'approved' | 'rejected' | 'review';

@Component({
  selector: 'app-approval-agents',
  imports: [
    CommonModule,
    RouterModule,
    ApprovalCardComponent,
    IconComponent,
    AvaTextboxComponent,
    ReactiveFormsModule,
    AvaTagComponent,
    ButtonComponent,
    PopupComponent,
    ConfirmationPopupComponent,
    ApprovalTxtCardComponent,
    CubicalLoadingComponent,
    PageFooterComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './approval-agents.component.html',
  styleUrls: ['./approval-agents.component.scss'],
})
export class ApprovalAgentsComponent implements OnInit {
  appLabels = approvalText.labels;

  public searchValue: string = '';
  public totalApprovedApprovals: number = 20;
  public totalPendingApprovals: number = 15;
  public totalApprovals: number = 60;
  public isBasicCollapsed: boolean = false;
  public quickActionsExpanded: boolean = true;
  public consoleApproval: any = {};
  public options: DropdownOption[] = [];
  public basicSidebarItems: any[] = [];
  public quickActions: any[] = [];
  public toolReviews: any[] = [];
  public workflowReviews: any[] = [];
  public filteredAgentsReviews: any[] = [];
  public agentsReviews: any[] = [];
  public currentToolsPage = 1;
  public currentAgentsPage = 1;
  public currentWorkflowsPage = 1;
  public pageSize = 2;
  public totalRecords = 0;
  public isDeleted = false;
  public currentTab = 'Agents';
  public showAgentApprovalPopup = false;
  public showInfoPopup = false;
  public showErrorPopup = false;
  public infoMessage = '';
  public selectedIndex = 0;
  public searchForm!: FormGroup;
  public labels: any = approvalText.labels;
  public approvedAgentId: number | null = null;
  public previewData: any = null;
  public selectedAgentId : number = 0;
  public isloading : boolean = true;
  public customButtons: DialogButton[] = [
    { label: 'Cancel', variant: 'secondary', action: 'cancel' },
    { label: 'Send Back', variant: 'primary', action: 'publish' }
];
  constructor(
    private router: Router,
    private apiService: SharedApiServiceService,
    private approvalService: ApprovalService,
    private fb: FormBuilder,
    private drawerService: DrawerService,
    private agentService: AgentServiceService,
    private dialogService: DialogService
  ) {
    this.labels = approvalText.labels;
    this.options = [
      { name: this.labels.electronics, value: 'electronics' },
      { name: this.labels.clothing, value: 'clothing' },
      { name: this.labels.books, value: 'books' },
    ];
    this.basicSidebarItems = [
      {
        id: '1',
        icon: 'hammer',
        text: this.labels.agents,
        route: '',
        active: true,
      },
      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },
      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },
    ];
    this.quickActions = [
      {
        icon: 'awe_agents',
        label: this.labels.agents,
        route: '',
      },
      {
        icon: 'awe_workflows',
        label: this.labels.workflows,
        route: '',
      },
      {
        icon: 'awe_tools',
        label: this.labels.tools,
        route: '',
      },
    ];
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchList();
    this.loadAgentsReviews();
    this.loadAgentsMetrics();
  }

  public loadAgentsMetrics(){
    this.isloading = true;
    this.approvalService.getAgentsMetrics().subscribe({
      next: (response) => {
        console.log('Agents metrics', response.agentsMetrics);
        const metrics = response?.agentMetrics;
        this.totalApprovals = metrics?.draftedAgentsCount;
        this.totalPendingApprovals = metrics?.reviewAgentsCount;
        this.totalApprovedApprovals = metrics?.approvedAgentsCount;
        this.isloading = false;
      }
    });
  }

  public searchList() {
    console.log(this.searchForm.get('search')?.value);
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.applyFilter(searchText);
      });
  }

  public applyFilter(text: string) {
    const lower = text;

    if (!text) {
      this.updateConsoleApproval(this.agentsReviews, 'agent');
      return;
    }

    this.filteredAgentsReviews = this.agentsReviews.filter((item) =>
      item.name?.toLowerCase().includes(lower),
    );
    this.updateConsoleApproval(this.filteredAgentsReviews, 'agent');
  }

  public onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }

  public uClick(i: any) {
    console.log('log' + i);
  }

  public toggleQuickActions(): void {
    this.quickActionsExpanded = !this.quickActionsExpanded;
  }

  public onBasicCollapseToggle(isCollapsed: boolean): void {
    this.isBasicCollapsed = isCollapsed;
    console.log('Basic sidebar collapsed:', isCollapsed);
  }

  public onBasicItemClick(item: any): void {
    this.basicSidebarItems.forEach((i) => (i.active = false));
    item.active = true;
    console.log(item);
  }

  public toRequestStatus(value: string | null | undefined): RequestStatus {
    return value === 'approved' || value === 'rejected' || value === 'review'
      ? value
      : 'review';
  }

  public loadAgentsReviews() {
    this.isloading = true;
    this.approvalService
      .getReviewsForAgents(this.currentAgentsPage, this.pageSize, this.isDeleted)
      .subscribe((response) => {
        this.agentsReviews = response?.agentReviewDetails;
        this.totalRecords = response?.totalNoOfRecords;
        this.filteredAgentsReviews = this.agentsReviews;
        // console.log('agents reviews ', this.agentsReviews);
        // this.totalRecords = this.agentsReviews.length;
        this.updateConsoleApproval(this.agentsReviews, 'agent');
        this.isloading = false;
      });
  }

  public loadMoreAgents(page: number) {
    this.currentAgentsPage = page;
    this.loadAgentsReviews();
  }

  public loadReviews(name: string) {
    this.loadAgentsReviews();
  }

  public rejectApproval(idx: any) {
    console.log(idx);
    this.selectedIndex = idx;
    this.dialogService.feedback({
      title: 'Confirm Send Back?',
      message: 'Are you sure you want to send back this workflow for corrections and modification?',
      variant: 'info',
      buttons: this.customButtons,
    }).then((result: any) => {
      console.log('Feedback dialog closed:', result);
      if(result.action === 'feedback'){
        this.handleAgentRejection(result.feedback);
      }
    });
  }

  public approveApproval(idx: any){
    console.log(idx);
    this.selectedIndex = idx;
    console.log(this.filteredAgentsReviews[this.selectedIndex]);
  }

  private showApprovalDialog(): void {
    this.dialogService.confirmation({
      title: this.labels.confirmApproval,
      message: `${this.labels.youAreAboutToApproveThis} Agent. ${this.labels.itWillBeActiveAndAvailableIn} Agents ${this.labels.catalogueForUsersToExecute}`,
      confirmButtonText: this.labels.approve,
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'danger',
      icon:'circle-check'
    }).then(result => {
      if (result.confirmed) {
        this.handleApproval();
      }
    });
  }

  private showFeedbackDialog(): void {
    const customButtons: DialogButton[] = [
          { label: 'Cancel', variant: 'secondary', action: 'cancel' },
          { label: 'Send Back', variant: 'primary', action: 'sendback' }
        ];
    this.dialogService.feedback({
      title: 'Confirm Send Back',
      message: 'This Agent will be send back for corrections and modification. Kindly comment what needs to be done.',
      buttons:customButtons,
      variant: 'info'
    }).then(result => {
      if (result.confirmed && result.confirmed === true) {
        this.handleRejection(result.data);
      }
    });
  }

  public handleApproval() {
    this.handleAgentApproval();
  }

  public handleRejection(feedback: any) {
    console.log("Clicked on confirmation popup");
    this.handleAgentRejection(feedback);
  }

  public onCardClick(index: number): void {
    console.log('Selected card index:', index);
    this.selectedIndex = index;
    const selectedAgent = this.filteredAgentsReviews[this.selectedIndex];
    this.selectedAgentId = selectedAgent.id;
    this.loadPreviewData(selectedAgent);

    this.drawerService.open(AgentsPreviewPanelComponent, {
      previewData: this.previewData,
      closePreview: () => this.drawerService.clear(),
      editAgent: () => this.handleEditAgent(selectedAgent.id),
      rejectApproval: () => this.handeMetaDataSendback(),
      approveApproval: () => this.handleMetaDataApproval(),
      testApproval: () => this.redirectToAgentsPlayground(),
    });
    console.log(selectedAgent);
  }

  public handleMetaDataApproval(){
    this.showApprovalDialog();
  }

  public handeMetaDataSendback(){
    this.showFeedbackDialog();
  }

  public handleEditAgent(agentId: string) {
    console.log('Edit Agent', agentId);
    this.drawerService.clear();
    this.router.navigate(
      ['/build/agents/collaborative'],
      { queryParams: { id: agentId, mode: 'edit' } }
    );
  }

  public handleAgentApproval() {
    const agentDetails = this.filteredAgentsReviews[this.selectedIndex];
    const id = agentDetails.id;
    const agentId = agentDetails.agentId;
    const status = 'APPROVED';
    const reviewedBy = agentDetails.reviewedBy;

    // Show loading dialog
    this.dialogService.loading({
      title: 'Approving Agent...',
      message: 'Please wait while we approve the agent.',
      showProgress: false,
      showCancelButton: false
    });

    this.approvalService
      .approveAgent(id, agentId, status, reviewedBy)
      .subscribe({
        next: (response: any) => {
          this.dialogService.close(); // Close loading dialog
          
          const message = response?.message || this.labels.agentSuccessApproveMessage;
          
          // Store agent ID for navigation after popup confirmation
          this.approvedAgentId = agentId;
          
          this.dialogService.success({
            title: 'Agent Approved',
            message: message,
          }).then(result => {
            if (result.action === 'secondary') {
              // Navigate to build agent screen with the approved agent ID
              this.router.navigate(['/build/agents/collaborative'], {
                queryParams: {
                  id: this.approvedAgentId,
                  mode: 'edit',
                },
              });
            } else {
              this.loadAgentsReviews(); // Refresh the list
            }
            // Reset the approved agent ID
            this.approvedAgentId = null;
          });
        },
        error: (error) => {
          this.dialogService.close(); // Close loading dialog
          
          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
          this.dialogService.error({
            title: 'Approval Failed',
            message: errorMessage,
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.handleAgentApproval();
            }
          });
        },
      });
  }

  public loadPreviewData(selectedAgent : any){
    this.previewData = {
      type: 'agent',
      title: selectedAgent.name,
      data: selectedAgent,
      loading: true,
      error: null,
    };
    // this.isloading = true;

    console.log('Load preview data', selectedAgent.id);
    this.agentService.getCollaborativeAgentDetailsById(selectedAgent.id).subscribe({
      next: (response) => {
        // console.log('Collaborative agent details', response);
        this.previewData.data = response.agentDetail;
        this.previewData.loading = false;
        this.previewData.error = null;
        // this.isloading = false;
      },
      error: (error) => {
        console.error('Error:', error);
        // this.isloading = false;
      },
    });
  }

  public handleAgentRejection(feedback: any) {
    const agentDetails = this.filteredAgentsReviews[this.selectedIndex];
    const id = agentDetails.id;
    const agentId = agentDetails.id;
    const status = 'REJECTED';
    const reviewedBy = agentDetails.reviewedBy ;
    const message = feedback;

    // Show loading dialog
    this.dialogService.loading({
      title: 'Rejecting Agent...',
      message: 'Please wait while we reject the agent.',
      showProgress: false,
      showCancelButton: false
    });

    this.approvalService
      .rejectAgent(id, agentId, status, reviewedBy, message)
      .subscribe({
        next: (response: any) => {
          this.dialogService.close(); // Close loading dialog
          
          const message = response?.message || this.labels.agentSuccessRejectMessage;
          this.dialogService.success({
            title: 'Agent Rejected',
            message: message
          }).then(() => {
            this.loadAgentsReviews(); // Refresh the list
          });
        },
        error: (error) => {
          this.dialogService.close(); // Close loading dialog
          
          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
          this.dialogService.error({
            title: 'Rejection Failed',
            message: errorMessage,
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.handleAgentRejection(feedback);
            }
          });
        },
      });
  }

  public handleTesting(index: any) {
    this.selectedAgentId = this.filteredAgentsReviews[index].id; 
    this.redirectToAgentsPlayground();
  }

  public redirectToAgentsPlayground(): void {
    this.router.navigate([`/build/agents/collaborative/execute`], {
      queryParams: { id: this.selectedAgentId },
    });
  }

  public updateConsoleApproval(data: any[], type: string) {
    this.consoleApproval = {
      contents: data?.map((req: any) => {
        const statusIcons: Record<RequestStatus, string> = {
          approved: 'circle-check-big',
          rejected: 'circle-x',
          review: 'clock',
        };
        const statusTexts: Record<RequestStatus, string> = {
          approved: this.labels.approved,
          rejected: this.labels.rejected,
          review: this.labels.review,
        };
        const statusKey = this.toRequestStatus(req?.status);
        const specificId = req.id;
        const title = req.name;

        return {
          id: req.id,
          refId: specificId,
          type: type,
          session1: {
            title: title,
            labels: [
              {
                name: type,
                color: 'success',
                background: 'red',
                type: 'normal',
              },
              {
                name: req.changeRequestType,
                color: req.changeRequestType === 'update' ? 'error' : 'info',
                background: 'red',
                type: 'pill',
              },
            ],
          },
          session2: [
            {
              name: type,
              color: 'default',
              background: 'red',
              type: 'normal',
            },
            {
              name: req.status,
              color: 'default',
              background: 'red',
              type: 'normal',
            },
          ],
          session3: [
            {
              iconName: 'user',
              label: req.createdBy,
            },
            {
              iconName: 'calendar-days',
              label: formatDate(req?.createdAt, 'dd MMM yyyy', 'en-IN'),
            },
          ],
          session4: {
            status: statusTexts[statusKey],
            iconName: statusIcons[statusKey],
          },
          session5: {
            org: req?.teamInfo?.org || 'Individual',
            domain: req?.teamInfo?.domain || 'Digital Ascender',
            project: req?.teamInfo?.project || 'Platform Engineering',
            team: req?.teamInfo?.team || 'Platform Engineering',
          },
        };
      }),
      footer: {},
    };
  }
}
