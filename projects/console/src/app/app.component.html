<div id="app-container" [class.login-active]="true">
  <div class="panel">
    <div #dialogHost></div>
  </div>
  <div class="glass-effect"></div>

  <!-- Shared Header Component -->
  <shared-app-header
    *ngIf="showHeaderAndNav"
    [config]="headerConfig"
    class="retractable-header"
    (navigationEvent)="onNavigation($event)"
    (profileAction)="onProfileAction($event)"
    (themeToggle)="onThemeToggle($event)"
    (languageChange)="onLanguageChange($event)"
    (orgConfigChange)="onOrgConfigChange($event)"
  >
  </shared-app-header>

  <div class="content-wrapper">
    <app-loader></app-loader>
    <router-outlet></router-outlet>
  </div>
</div>
<!-- <footer>© 2025 Ascendion. All Rights Reserved</footer> -->
