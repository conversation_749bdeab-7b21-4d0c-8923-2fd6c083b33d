import {
  Component,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';
import { environment } from '../environments/environment';
import { SharedAppHeaderComponent, HeaderConfig } from '@shared/components/app-header/app-header.component';
import { experienceStudioHeaderConfig } from './config/header.config';
import { ThemeService } from './shared/services/theme-service/theme.service';


@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    SharedAppHeaderComponent
],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  showHeaderAndNav: boolean = true;
  redirectUrl = '';
  // Header configuration
  headerConfig: HeaderConfig = experienceStudioHeaderConfig;

  constructor(
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private router: Router,
    public themeService: ThemeService,
    private centralizedRedirectService: CentralizedRedirectService,
  ) {}

  ngOnInit(): void {
    // const savedTheme = this.themeService.getCurrentTheme();
    // this.themeService.setTheme(savedTheme);
    
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.experienceApiUrl,
      redirectUrl: environment.experianceRedirectUrl,
      postLoginRedirectUrl: '/',
      appName: 'experience-studio',
    };
    this.authService.setAuthConfig(authConfig);
    
    // Check authentication status and redirect if needed
    if (!this.checkAuthenticationAndRedirect()) {
      return; // Don't continue if not authenticated
    }
    
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    
    // org_path is now set during login, no need to check here
  }

  ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
  }

  // Simple authentication check
  private checkAuthenticationAndRedirect(): boolean {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();
    
    if (!accessToken && !refreshToken) {
      // Store current URL and redirect to marketing login
      this.centralizedRedirectService.storeIntendedDestination(window.location.href);
      this.centralizedRedirectService.redirectToMarketingLogin();
      return false;
    }
    return true;
  }

  // Header event handlers
  onNavigation(route: string): void {
    console.log('Experience Studio Navigation to:', route);
  }
   onLogout() {
    if (this.tokenStorage.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          // Use Angular router instead of window.location
          this.router.navigate(['/login']);
          this.tokenStorage.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('Basic logout failed:', error);
          // Still try to navigate to login even if logout fails
          this.router.navigate(['/login']);
        },
      });
    } else {
      // For SSO logout, redirect to marketing login
      this.centralizedRedirectService.redirectToMarketingLogin();
    }
  }

  onProfileAction(action: string): void {
    console.log('Profile action:', action);
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    this.themeService.setTheme(theme);
  }
}
