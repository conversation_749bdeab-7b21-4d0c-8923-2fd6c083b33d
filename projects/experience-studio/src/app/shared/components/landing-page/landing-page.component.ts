import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject,
  signal,
  computed,
  DestroyRef,
  ElementRef,
  AfterViewInit
} from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { HeroSectionHeaderComponent } from '../hero-section-header/hero-section-header.component';
import { RecentCreationComponent } from '../recent-creation/recent-creation.component';
import { CardDataService } from '../../services/data-services/card-data.service';
import { ThemeService } from '../../services/theme-service/theme.service';
import { ToastService } from '../../services/toast.service';
import { CardSelectionService } from '../../services/card-selection.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';
import { createThemeManager, THEME_COLORS, getThemeColor } from '../../utils/theme-manager.util';
import { PerformanceMonitor } from '../../utils/performance-monitor.util';
import { ImageOptimizationService } from '../../services/image-optimization.service';
import { StrategicPreloadingService } from '../../services/strategic-preloading.service';
import { HealthCheckService, HealthCheckResponse } from '../../services/health-check.service';

interface StudioCard {
  readonly id: number;
  readonly title: string;
  readonly description: string;
  readonly image: string;
  readonly path: string;
  readonly type: string;
  readonly disabled?: boolean;
  readonly priority?: 'high' | 'low'; // For image loading priority
  readonly isLoading?: boolean; // ENHANCED: Loading state for health check
  readonly alwaysDisabled?: boolean; // Flag for permanently disabled cards
}

type Theme = 'light' | 'dark';

const STUDIO_CARDS: readonly StudioCard[] = [
{
  id: 1,
  title: 'Generate Wireframes',
  description: 'Generate clean, structured wireframes from your ideas in seconds for faster design planning.',
  image: 'assets/cards-images/ui_design.svg',
  path: 'prompt',
  type: 'generate-ui-design',
  disabled: true,
  priority: 'high'
},
{
  id: 2,
  title: 'Image to Application',
  description: 'Convert UI images or sketches into working application code instantly without manual effort.',
  image: 'assets/cards-images/app_generation.svg',
  path: 'prompt',
  type: 'image-to-application',
  disabled: true,
  priority: 'high'
},
{
  id: 3,
  title: 'Prompt to Application',
  description: 'Turn natural language prompts into ready-to-use applications with ease and speed.',
  image: 'assets/cards-images/prompt-to-code.svg',
  path: 'prompt',
  type: 'prompt-to-application',
  disabled: true,
  priority: 'high',
},
{
  id: 4,
  title: 'Design Accessibility',
  description: 'Ensure your designs are accessible and inclusive for everyone across all platforms.',
  image: 'assets/cards-images/design_analysis.svg',
  path: '#',
  type: 'design-accessibility',
  disabled: true,
  priority: 'low',
  alwaysDisabled: true
}
] as const;

@Component({
  selector: 'app-landing-page',
  standalone: true,
  imports: [
    CommonModule,
    HeroSectionHeaderComponent,
    RecentCreationComponent,
  ],
  templateUrl: './landing-page.component.html',
  styleUrl: './landing-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LandingPageComponent implements OnInit, AfterViewInit {
  private readonly router = inject(Router);
  private readonly cardDataService = inject(CardDataService);
  private readonly toastService = inject(ToastService);
  private readonly cardSelectionService = inject(CardSelectionService);
  private readonly elementRef = inject(ElementRef);
  private readonly destroyRef = inject(DestroyRef);
  private readonly imageOptimizationService = inject(ImageOptimizationService);
  private readonly strategicPreloadingService = inject(StrategicPreloadingService);
  private readonly healthCheckService = inject(HealthCheckService);

  // Modern theme management using functional approach
  private readonly themeManager = createThemeManager();

  // ENHANCED: Health check state management using Angular 19+ Signals
  private readonly healthCheckLoading = signal<boolean>(true);
  private readonly healthCheckError = signal<string | null>(null);
  private readonly servicesHealthy = signal<boolean>(false);

  // ENHANCED: Computed studio cards with dynamic disabled state based on health check
  readonly studioCards = computed(() => {
    const isHealthy = this.servicesHealthy();
    const isLoading = this.healthCheckLoading();

    return STUDIO_CARDS.map(card => ({
      ...card,
      disabled: card.alwaysDisabled || !isHealthy || isLoading, // Always disable if alwaysDisabled is true
      isLoading: isLoading // Add loading state for template
    }));
  });

  // Performance monitoring
  private performanceId = '';

  // Angular 19+ Signals for reactive theme management
  readonly currentTheme = signal<Theme>(this.themeManager.theme());

  // Computed theme colors using Angular Signals (more efficient than getters)
  readonly cardTextColor = computed(() => getThemeColor('text', this.currentTheme()));
  readonly cardDescriptionColor = computed(() => getThemeColor('description', this.currentTheme()));
  readonly cardBorderColor = computed(() => getThemeColor('border', this.currentTheme()));
  readonly cardBackground = computed(() => 'transparent'); // Consistent transparent background

  // Image loading state management using the optimization service
  readonly imageLoadingStates = signal<Map<number, 'loading' | 'loaded' | 'error'>>(new Map());

  readonly trackByCardId = (_: number, card: StudioCard): number => card.id;

  /**
   * Get theme-aware divider image with computed optimization
   */
  readonly dividerImage = computed(() =>
    this.themeManager.getThemeAsset('assets/icons/divider', 'svg')
  );

  /**
   * Get image source (keeping original SVG images)
   */
  getOptimizedImageSrc(card: StudioCard): string {
    return card.image;
  }

  /**
   * Get image loading priority
   */
  getImagePriority(card: StudioCard): 'high' | 'low' {
    return card.priority || 'low';
  }

  /**
   * Check if image is loaded
   */
  isImageLoaded(cardId: number): boolean {
    return this.imageLoadingStates().get(cardId) === 'loaded';
  }

  /**
   * Check if image is loading
   */
  isImageLoading(cardId: number): boolean {
    return this.imageLoadingStates().get(cardId) === 'loading';
  }

  // Route mappings for O(1) lookup performance
  private readonly routeMap = new Map<string, string>([
    ['Generate Wireframes', '/generate-ui-design'],
    ['Generate Application', '/generate-application'],
    ['Image to Application', '/generate-application'],
    ['Prompt to Application', '/generate-application']
  ]);

  private readonly actionMap = new Map<string, string>([
    ['Generate Wireframes', 'UI'],
    ['Generate Application', 'Application'],
    ['Image to Application', 'Application'],
    ['Prompt to Application', 'Application']
  ]);

  ngOnInit(): void {
    // Start performance monitoring
    this.performanceId = PerformanceMonitor.start('LandingPageComponent.init');

    // ENHANCED: Perform health check as highest priority operation
    this.performHealthCheck();

    // Setup theme change subscription using Angular 19+ patterns
    this.themeManager.onThemeChange((newTheme) => {
      this.currentTheme.set(newTheme);
    });

    // Initialize image loading states
    this.initializeImageStates();

    // Preload critical SVG images
    this.preloadCriticalImagesOptimized();

    // Setup strategic route preloading
    this.strategicPreloadingService.preloadStrategicRoutes('/experience/main');

    // Theme management is now handled automatically by themeManager
    this.cardSelectionService.resetSelectionState();

    // End performance monitoring
    PerformanceMonitor.stop(this.performanceId);
  }

  ngAfterViewInit(): void {
    // Setup intersection observer for progressive loading
    this.setupIntersectionObserver();
  }

  navigateToStudio(card: StudioCard, event: Event): void {
    event.stopPropagation();
    event.preventDefault();

    // ENHANCED: Strict disabled state checking with multiple conditions
    const isHealthCheckLoading = this.healthCheckLoading();
    const isServicesHealthy = this.servicesHealthy();
    const isCardDisabled = card.disabled || !isServicesHealthy || isHealthCheckLoading;

    if (isCardDisabled) {
      // ENHANCED: Provide specific feedback based on the reason for being disabled
      if (isHealthCheckLoading) {
        this.toastService.info('Please wait while we check service availability...');
      } else if (!isServicesHealthy) {
        this.toastService.error('Please contact admin to restart the app services');
      } else {
        this.toastService.info('This feature is in build mode.');
      }
      return;
    }

    // ENHANCED: Double-check health status before navigation
    if (!this.servicesHealthy()) {
      this.toastService.error('Services are not available. Please contact admin to restart the app services');
      return;
    }

    this.cardDataService.setSelectedCardTitle(card.title);
    this.cardSelectionService.setCardSelected(true);

    const routePrefix = this.getRoutePrefix(card.title);
    const actionType = this.getActionType(card.title);

    this.toastService.info(`Starting ${actionType} generation`);
    this.router.navigate([routePrefix]);
  }

  private getRoutePrefix(title: string): string {
    return this.routeMap.get(title) || '/generate-application';
  }

  private getActionType(title: string): string {
    return this.actionMap.get(title) || 'Application';
  }

  /**
   * Preload critical images (simplified for SVG images)
   */
  private async preloadCriticalImagesOptimized(): Promise<void> {
    const criticalCards = this.studioCards().filter(card => card.priority === 'high');

    // Preload critical SVG images
    criticalCards.forEach(card => {
      const img = new Image();
      img.onload = () => this.updateImageState(card.id, 'loaded');
      img.onerror = () => this.updateImageState(card.id, 'error');
      img.src = card.image;
    });

    // Also preload the divider image
    const dividerImg = new Image();
    dividerImg.src = this.dividerImage();
  }

  /**
   * Initialize image loading states for all cards
   */
  private initializeImageStates(): void {
    const states = new Map<number, 'loading' | 'loaded' | 'error'>();
    this.studioCards().forEach(card => {
      states.set(card.id, 'loading');
    });
    this.imageLoadingStates.set(states);
  }

  /**
   * Setup intersection observer using the optimization service
   */
  private setupIntersectionObserver(): void {
    const observer = this.imageOptimizationService.createLazyLoadObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const cardElement = entry.target as HTMLElement;
            const cardId = parseInt(cardElement.dataset['cardId'] || '0', 10);

            if (cardId && this.imageLoadingStates().get(cardId) === 'loading') {
              this.loadImageProgressively(cardId);
            }
          }
        });
      },
      {
        root: null,
        rootMargin: '50px',
        threshold: 0.1
      }
    );

    if (!observer) {
      // Fallback for browsers without IntersectionObserver
      return;
    }

    // Observe all card elements after view init
    setTimeout(() => {
      const cardElements = this.elementRef.nativeElement.querySelectorAll('.studio-card');
      cardElements.forEach((element: Element) => observer.observe(element));
    }, 0);
  }

  /**
   * Update image loading state
   */
  private updateImageState(cardId: number, state: 'loading' | 'loaded' | 'error'): void {
    const currentStates = this.imageLoadingStates();
    const newStates = new Map(currentStates);
    newStates.set(cardId, state);
    this.imageLoadingStates.set(newStates);
  }

  /**
   * Load image progressively when it comes into view
   */
  private loadImageProgressively(cardId: number): void {
    const card = this.studioCards().find(c => c.id === cardId);
    if (!card) return;

    const img = new Image();
    img.onload = () => this.updateImageState(cardId, 'loaded');
    img.onerror = () => this.updateImageState(cardId, 'error');
    img.src = card.image;
  }

  /**
   * Handle image load event from template
   */
  onImageLoad(cardId: number): void {
    this.updateImageState(cardId, 'loaded');
  }

  /**
   * Handle image error event from template
   */
  onImageError(cardId: number): void {
    this.updateImageState(cardId, 'error');
  }

  /**
   * ENHANCED: Perform health check to determine card availability
   * This is the highest priority operation called during component initialization
   * ENHANCED: Removed duplicate toast messages - health check service handles error notifications
   */
  private performHealthCheck(): void {
    this.healthCheckLoading.set(true);
    this.healthCheckError.set(null);

    this.healthCheckService.performHealthCheck()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response) => {
          const isHealthy = response.status === 'ok';
          this.servicesHealthy.set(isHealthy);
          this.healthCheckLoading.set(false);

          if (!isHealthy) {
            const errorMessage = response.message || 'App Services are down. Please Contact admin to restart the service';
            this.healthCheckError.set(errorMessage);
            // ENHANCED: Removed duplicate toast - health check service handles error notifications
          }
        },
        error: () => {
          this.servicesHealthy.set(false);
          this.healthCheckLoading.set(false);
          const errorMessage = 'App Services are down. Please Contact admin to restart the service';
          this.healthCheckError.set(errorMessage);
          // ENHANCED: Removed duplicate toast - health check service handles error notifications
        }
      });
  }

  /**
   * ENHANCED: Get health check loading state for template
   */
  get isHealthCheckLoading(): boolean {
    return this.healthCheckLoading();
  }

  /**
   * ENHANCED: Get health check error message for template
   */
  get healthCheckErrorMessage(): string | null {
    return this.healthCheckError();
  }

  /**
   * ENHANCED: Check if services are healthy for template
   */
  get areServicesHealthy(): boolean {
    return this.servicesHealthy();
  }
}
