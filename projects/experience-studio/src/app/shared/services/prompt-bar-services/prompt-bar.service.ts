import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router, ActivatedRoute } from '@angular/router';
import { environment } from '../../../../environments/environment';
import { AppStateService } from '../app-state.service';
import { UserSignatureService } from '../user-signature.service';

export interface PromptData {
  imageFile?: File | null;
  imageUrl?: string | null;
  imageDataUri?: string | null;
  prompt: string;
  enhancedPrompt?: string;
  selectedCardTitle?: string;
}
export interface CompleteSelections {
  type: string;
  prompt: string;
  application: string | null;
  technology: string | null;
  designLibrary: string | null;
  imageFile?: File | null;
  imageUrl?: string | null;
  imageDataUri?: string | null;
  textContent?: string | null; // Add text content property
  projectId?: string;
  jobId?: string | null;
}

@Injectable({
  providedIn: 'root',
})
export class PromptBarService {
  private promptDataSubject = new BehaviorSubject<PromptData>({
    prompt: '',
  });

  private promptData: PromptData = { prompt: '' };
  private completeSelections: CompleteSelections | null = null;

  public promptData$ = this.promptDataSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router,
    private route: ActivatedRoute,
    private appStateService: AppStateService,
    // COMMENTED OUT: userSignature functionality removed
    // private userSignatureService: UserSignatureService
  ) {}

  setImage(file: File | null): void {
    const currentData = this.promptDataSubject.getValue();

    // If file is null, clear the image data but keep the prompt
    if (!file) {
      this.promptDataSubject.next({
        ...currentData,
        enhancedPrompt: undefined, // Reset the enhanced prompt when image is removed
        imageFile: null,
        imageUrl: null,
        imageDataUri: null,
      });
      return;
    }

    // Process the file
    const imageUrl = URL.createObjectURL(file as Blob);

    const reader = new FileReader();
    reader.onload = e => {
      const imageDataUri = e.target?.result as string;

      // Make sure we're using the most up-to-date data
      const latestData = this.promptDataSubject.getValue();

      // Keep the existing prompt when a new image is uploaded
      this.promptDataSubject.next({
        ...latestData,
        enhancedPrompt: undefined, // Reset enhanced prompt when a new image is uploaded
        imageFile: file,
        imageUrl: imageUrl,
        imageDataUri: imageDataUri,
        // Preserve the current prompt to prevent overwriting user input
        prompt: latestData.prompt
      });
    };
    reader.readAsDataURL(file);
  }

  setPrompt(prompt: string): void {
    const currentData = this.promptDataSubject.getValue();
    this.promptDataSubject.next({
      ...currentData,
      prompt,
    });
  }

  /**
   * Sets the enhanced prompt in the prompt data
   * This updates both the prompt and enhancedPrompt fields
   * @param enhancedPrompt The enhanced prompt to set
   */
  setEnhancedPrompt(enhancedPrompt: string): void {
    const currentData = this.promptDataSubject.getValue();
    this.promptDataSubject.next({
      ...currentData,
      prompt: enhancedPrompt,
      enhancedPrompt,
    });
  }
  setSelectedCardTitle(title: string): void {
    const currentData = this.promptDataSubject.getValue();
    this.promptDataSubject.next({
      ...currentData,
      selectedCardTitle: title,
    });
  }

  enhancePrompt(
    currentPrompt: string,
    type: string,
    imageDataUris: string[] = [],
    userSignature?: string
  ): Observable<any> {
    // COMMENTED OUT: userSignature functionality removed
    // Get user signature from service if not provided
    // const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    const apiUrl = environment.experienceApiUrl;

    const endPoint = '/common/enhance';

    // Ensure type is always "Generate Application" for consistency
    const actualType = type || 'Generate Application';

    const typeMap = {
      'Generate Wireframes': 'wireframe_generation',
    'Generate Application': 'app_generation',
    };

    const mappedType = typeMap[actualType as keyof typeof typeMap] || 'app_generation'; // Default to app_generation if not found

    // Create the payload with the correct structure
    // Always include the image parameter, even if it's an empty array
    const payload: any = {
      type: mappedType,
      prompt: currentPrompt,
      image: [], // Default to empty array
      // userSignature: signature,
    };

    // Add image data URIs to the payload if available using the key 'image'
    if (imageDataUris && imageDataUris.length > 0) {
      payload.image = imageDataUris;
    }

    return this.http.post<any>(apiUrl + endPoint, payload);
  }

  submitPromptData(): void {
    const currentData = this.promptDataSubject.getValue();

    // Ensure selectedCardTitle is always "Generate Application"
    if (!currentData.selectedCardTitle || currentData.selectedCardTitle !== 'Generate Application') {
      this.setSelectedCardTitle('Generate Application');
      currentData.selectedCardTitle = 'Generate Application';
    }

    const dataToStore = {
      ...currentData,
      imageFile: null,
      imageUrl: currentData.imageUrl,
      imageDataUri: currentData.imageDataUri,
    };
    // Store in sessionStorage for backward compatibility
    sessionStorage.setItem('promptData', JSON.stringify(dataToStore));

    // Update the app state with the prompt data
    this.appStateService.updateProjectState({
      prompt: currentData.prompt,
      imageUrl: currentData.imageUrl,
      imageDataUri: currentData.imageDataUri,
      type: currentData.selectedCardTitle || 'Generate Application',
    });
    if (currentData.imageFile) {
      this.setCompleteSelections({
        ...(this.completeSelections || {}),
        imageFile: currentData.imageFile,
        imageUrl: currentData.imageUrl,
        type: 'Generate Application',  // Always use Generate Application
        imageDataUri: currentData.imageDataUri,
        prompt: currentData.prompt,
        application: null,
        technology: null,
        designLibrary: null,
      });
      // this.router.navigateByUrl('/experience/image-to-code/form');
    } else if (currentData.prompt) {
      // this.router.navigateByUrl('/experience/prompt-to-code/form');
    }
  }

  getCurrentPromptData(): PromptData {
    return this.promptDataSubject.getValue();
  }

  setCompleteSelections(data: CompleteSelections): void {
    // Store in local state for backward compatibility
    this.completeSelections = data;

    // Update the centralized app state
    this.appStateService.setCompleteSelections(data);
  }

  getCompleteSelections(): CompleteSelections | null {
    return this.completeSelections;
  }

  /**
   * Resets the enhanced prompt state
   * This is useful when the user removes an image or uploads a new one
   */
  resetEnhancedPromptState(): void {
    const currentData = this.promptDataSubject.getValue();
    this.promptDataSubject.next({
      ...currentData,
      enhancedPrompt: undefined,
      // Ensure we don't accidentally overwrite the current prompt
      prompt: currentData.prompt
    });
  }
}
