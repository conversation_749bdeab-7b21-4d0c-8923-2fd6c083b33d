.container-fluid {
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

:host ::ng-deep {
  .outer-box .container {
    background: none;
  }
  .outer-box .center-content-wrapper{
    margin-top:2rem !important;
  }

  .header-shadow {
    background: none !important;
  }

  .nav-menu .nav-items {
    stroke-width: 1px !important;
    transition: all 0.3s ease !important;
    background: var(--nav-items-fill) !important;
    stroke: var(--nav-items-stroke) !important;
    // opacity:50% !important;
  }
  .header-wrapper .header-shadow{
    background:none !important;
  }
  .item-label{
    color:var(--body-text-color) !important;
  }
  // Generic nav-items styles if needed
  .nav-items {
    stroke-width: 1px;
    transition: all 0.3s ease;
    background: var(--nav-items-fill);
    stroke: var(--nav-items-stroke);
  }

  .ava-dropdown .dropdown-toggle{
    background: transparent !important;
    border: 0.5px solid var(--prompt-bar-border-color) !important;
  }



.org-path-dropdown-container {
  .org-path-trigger {

    background:transparent !important;

  }

  .org-path-dropdown-container{

    background:transparent !important;
  }

  .org-path-popover{

    background:transparent !important;

  }
}
}
