import { Component, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';
import { environment } from '../environments/environment';
import { SharedAppHeaderComponent, HeaderConfig } from '@shared/components/app-header/app-header.component';
import { elderWandHeaderConfig } from './config/header.config';
import { ThemeService } from './shared/services/theme.service';

import { GlobalStoreService } from './shared/service/global-store.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, CommonModule, SharedAppHeaderComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  showHeaderAndNav: boolean = true;
  showHeroSection: boolean = true;
  isLaunchpadRoute: boolean = false;
  
  // Header configuration
  headerConfig: HeaderConfig = elderWandHeaderConfig;

  constructor(
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private router: Router,
    private globalStoreService: GlobalStoreService,
    public themeService: ThemeService,
    private centralizedRedirectService: CentralizedRedirectService,
  ) {}

  ngOnInit(): void {
    const savedTheme = this.themeService.getCurrentTheme();
    this.themeService.setTheme(savedTheme);
    
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.elderWandApiAuthUrl,
      redirectUrl: environment.elderWandRedirectUrl,
      postLoginRedirectUrl: '/',
      appName: 'elder-wand',
    };
    this.authService.setAuthConfig(authConfig);
    
    // Check authentication status and redirect if needed
    if (!this.checkAuthenticationAndRedirect()) {
      return; // Don't continue if not authenticated
    }
    
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    
    // org_path is now set during login, no need to check here
    
    this.router.events.subscribe(() => {
      if (this.router.url === '/login') {
        this.showHeaderAndNav = false;
        this.showHeroSection = false;
        this.isLaunchpadRoute = false;
      } else if (this.router.url === '/marketplace') {
        // Hide header and nav for marketplace (it has its own layout)
        this.showHeaderAndNav = false;
        this.showHeroSection = false;
        this.isLaunchpadRoute = false;
      } else if (this.router.url === '/' || this.router.url === '') {
        // Hide header and nav for root redirect component
        this.showHeaderAndNav = false;
        this.showHeroSection = false;
        this.isLaunchpadRoute = false;
      } else {
        this.showHeaderAndNav = true;
        // Hide hero section for my-agent-home page, show for all other pages
        this.showHeroSection = this.router.url !== '/my-agent-home';
        // Show background image only for dashboard (launchpad) route
        this.isLaunchpadRoute =
          this.router.url === '/dashboard' || this.router.url === '/';
      }
    });
  }

  ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
  }

  // Simple authentication check
  private checkAuthenticationAndRedirect(): boolean {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();
    
    if (!accessToken && !refreshToken) {
      // Store current URL and redirect to marketing login
      this.centralizedRedirectService.storeIntendedDestination(window.location.href);
      this.centralizedRedirectService.redirectToMarketingLogin();
      return false;
    }
    return true;
  }

  onNavigation(route: string): void {
    console.log('Elder Wand Navigation to:', route);
  }

  onProfileAction(action: string): void {
    console.log('Profile action:', action);
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    this.themeService.setTheme(theme);
  }

  getSelectedUser(user: any) {
    console.log('Selected user:', user);
  }

  onAscendionOptionSelected(option: string) {
    console.log('Ascendion option selected:', option);
  }
}
