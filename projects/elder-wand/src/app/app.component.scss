.container-fluid {
  padding: 0;
  min-height: 100vh;

  // Ensure content appears above any overlay
  > * {
    position: relative;
    z-index: 11;
  }

  // Background image styling for launchpad only
  &.launchpad-bg {
    background-image: url("../assets/icons/background.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;

    // Optional: Add a subtle overlay for better text readability
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      pointer-events: none;
      z-index: 0;
    }
  }

  // White background for all other pages
  &.white-bg {
    background: #ffffff;
  }
}
