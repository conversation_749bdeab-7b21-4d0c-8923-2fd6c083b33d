.my-agent-home-container {
  margin: 0 auto;
  background: white;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1rem;
}

.user-profile-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  .avatar-image {
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(20, 70, 146, 0.08);
    margin-bottom: 26px;
  }
}

.user-details {
  .user-name {
    color: #000;
    font-family: Mulish;
    font-size: 32px;
    font-weight: 700;
    margin: 0;
  }

  .user-email,
  .user-role {
    color: #616874;
    font-family: Mulish;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.create-agent-btn {
  .ava-button .button-label {
    color: #fff;
  }
}

// Statistics Cards Section
.stats-section {
  margin: 2rem;

  .row {
    margin: 0 -0.5rem;
  }

  .col-12,
  .col-sm-6,
  .col-lg-3 {
    padding: 0 0.5rem;
  }
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border: 1px solid var(--Brand-Neutral-n-50, #f0f1f2);
  box-shadow: var(--Elevation-01X) var(--Elevation-01Y) var(--Elevation-01Blur)
    var(--Elevation-01Spread) var(--BrandNeutraln-50);

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.stat-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.stat-icon-container {
  flex-shrink: 0;

  .stat-icon {
    width: 42px;
    height: 42px;
    border-radius: 8px;
    background: linear-gradient(180deg, #fde9ef 0%, #f0ebf8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    svg {
      stroke: black !important;
    }
  }
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.stat-title {
  color: #000;
  text-align: left;
  font-family: Mulish;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.stat-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-star {
  flex-shrink: 0;
}

.stat-value {
  color: #3b3f46;
  font-family: Mulish;
  font-size: 32px;
  font-weight: 700;
  margin: 0;
}

// Responsive design
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;

    ava-button {
      width: 100%;
      min-width: unset;
    }
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 576px) {
  .stat-card {
    gap: 0.75rem;
  }

  .stat-title {
    font-size: 0.8rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }
}

// My Agents Section
.my-agents-section {
  margin: 2rem 2rem 0 2rem;
  .section-title {
    color: #000;
    font-family: Mulish;
    font-size: 32px;
    font-weight: 700;
    margin: 0.5rem 0;
  }
}

// Agent Cards Grid
.agent-cards-grid {
  margin-top: 1rem;

  .row {
    margin: 0 -0.5rem;
  }

  .col-12,
  .col-md-6,
  .col-lg-4 {
    padding: 0 0.5rem;
    margin-bottom: 1.5rem;
  }
}

.agent-card {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  border: 1px solid #4383e633;
  box-shadow: var(--Elevation-01X) var(--Elevation-01Y) var(--Elevation-01Blur)
    var(--Elevation-01Spread) var(--BrandNeutraln-50);

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .card-layout {
    display: flex;
    gap: 1rem;
    height: 100%;
  }

  .agent-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(180deg, #f06896 0%, #997bcf 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 0 0 1px #e0e0e0;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    flex: 1;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .agent-title {
      color: #000;
      font-family: Mulish;
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      flex: 1;
    }

    .status-badge {
      color: #fff;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      padding: 5px 10px;
      border-radius: 20px;

      &.status-approved {
        background: #059669;
        color: #fff;
      }

      &.status-pending {
        background: #6b7280;
        color: #fff;
      }

      &.status-denied {
        background: #dc2626;
        color: #fff;
      }
    }
  }

  .agent-description {
    color: #595959;
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
    margin: 0;
  }

  .agent-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .created-date {
    font-size: 16px;
    color: #595959;
    margin: 0;
  }

  .icons {
    display: flex;
    gap: 20px;
  }

  ava-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .play-icon {
    border: 1px solid #e91e63;
    padding: 0 20px;
    border-radius: 8px;
  }
}
.pagination {
  margin: 0 2rem 1rem 2rem;
  padding: 0;
}
.page-footer {
  margin: 0;
  padding: 0;
}
.page-container {
  padding: 0;
}

// Responsive styles for agent cards
@media (max-width: 768px) {
  .agent-cards-grid {
    .col-12,
    .col-md-6,
    .col-lg-4 {
      margin-bottom: 1rem;
    }
  }

  .agent-card {
    padding: 1rem;

    .card-layout {
      gap: 0.75rem;
    }

    .agent-icon {
      width: 40px;
      height: 40px;
    }

    .agent-title {
      font-size: 18px;
    }

    .agent-description {
      font-size: 14px;
    }

    .agent-meta .created-date {
      font-size: 14px;
    }

    .status-badge {
      padding: 0.2rem 0.5rem;
      font-size: 0.7rem;
    }

    .card-actions {
      gap: 0.4rem;
      flex-direction: column;

      ava-button {
        width: 100%;

        &:last-child {
          width: 40px;
          align-self: flex-end;
        }
      }
    }
  }
}

// Medium screens
@media (max-width: 992px) {
  .agent-card {
    .card-actions {
      ava-button {
        width: 100%;

        &:last-child {
          width: 44px;
        }
      }
    }
  }
}

// Additional responsive breakpoints for agent cards
@media (max-width: 576px) {
  .agent-card {
    padding: 0.75rem;

    .card-layout {
      gap: 0.5rem;
    }

    .agent-icon {
      width: 36px;
      height: 36px;
    }

    .agent-title {
      font-size: 16px;
    }

    .agent-description {
      font-size: 13px;
    }

    .agent-meta .created-date {
      font-size: 12px;
    }

    .card-actions {
      gap: 0.3rem;

      ava-button {
        font-size: 12px;
        height: 36px;

        &:last-child {
          width: 36px;
          height: 36px;
        }
      }
    }
  }
}
