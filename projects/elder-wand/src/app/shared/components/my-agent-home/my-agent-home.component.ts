import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { GlobalStoreService } from '../../service/global-store.service';
import { IconComponent, ButtonComponent } from "@ava/play-comp-library";
import { FilterTabsComponent, FilterTab } from '../filter-tabs/filter-tabs.component';
import { PageFooterComponent } from "@shared/index";
import { Router } from '@angular/router';

interface User {
  name: string;
  role: string;
  type: string;
}

interface StatCard {
  icon: string;
  title: string;
  value: string;
  iconColor: string;
}

interface AgentCard {
  id: number;
  title: string;
  description: string;
  status: 'Approved' | 'Pending' | 'Denied' | 'Draft';
  createdDate: string;
}

@Component({
  selector: 'app-my-agent-home',
  imports: [CommonModule, IconComponent, FilterTabsComponent, ButtonComponent, PageFooterComponent],
  templateUrl: './my-agent-home.component.html',
  styleUrl: './my-agent-home.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class MyAgentHomeComponent implements OnInit {

  
  userName: string = '';
  userEmail: string = '';
  userRole: string = 'Lead Backend Engineer';
  userAvatar: string = 'assets/icons/user-avatar.svg';
  selectedUser: User | null = null;
  currentPage: number = 1;
    itemsPerPage: number = 12;


  // Statistics cards data
  statCards: StatCard[] = [
    {
      icon: 'bot',
      title: 'Total Agents created',
      value: '32',
      iconColor: 'white'
    },
    {
      icon: 'circle-check',
      title: 'Approved Agents',
      value: '126',
      iconColor: 'white'
    },
    {
      icon: 'star',
      title: 'Average Rating',
      value: '4.8',
      iconColor: 'white'
    },
    {
      icon: 'chart-spline',
      title: 'Average Accuracy',
      value: '97%',
      iconColor: 'white'
    }
  ];

  // Tabs data for My Agents section
  activeTab = 'all';
  agentTabs: FilterTab[] = [
    { id: 'all', label: 'All', priority: 100 },
    { id: 'approved', label: 'Approved (126)', icon: 'circle-check', iconColor: '#059669', priority: 90 },
    { id: 'pending', label: 'Pending (10)', icon: 'clock', iconColor: '#D97706', priority: 80 },
    { id: 'denied', label: 'Denied (4)', icon: 'x', iconColor: '#DC2626', priority: 70 },
    { id: 'draft', label: 'Draft (1)', icon: 'pencil', priority: 60 }
  ];

  // Agent cards data
  allAgentCards: AgentCard[] = [
  {
    id: 1,
    title: 'Java to Ruby Migration',
    description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
    status: 'Approved',
    createdDate: '12/06/2025'
  },
  {
    id: 2,
    title: 'Java to Ruby Migration',
    description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
    status: 'Pending',
    createdDate: '10/06/2025'
  },
  {
    id: 3,
    title: 'Java to Ruby Migration',
    description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
    status: 'Denied',
    createdDate: '11/06/2025'
  },
  {
    id: 4,
    title: 'Java to Ruby Migration',
    description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
    status: 'Approved',
    createdDate: '08/06/2025'
  },
  {
    id: 5,
    title: 'Java to Ruby Migration',
    description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
    status: 'Draft',
    createdDate: '05/06/2025'
  },
  {
    id: 6,
    title: 'Java to Ruby Migration',
    description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
    status: 'Pending',
    createdDate: '01/06/2025'
  },
  {
    id: 7,
    title: 'Python to Go Migration',
    description: 'Migration tool to transition backend services from Python to Go with minimal manual intervention.',
    status: 'Denied',
    createdDate: '23/05/2025'
  },
  {
    id: 8,
    title: 'Database Upgrade',
    description: 'Upgrade database systems from MySQL 5.7 to MySQL 8.0.',
    status: 'Pending',
    createdDate: '19/05/2025'
  },
  {
    id: 9,
    title: 'Cloud Migration',
    description: 'Migrate on-premise servers to AWS cloud infrastructure.',
    status: 'Approved',
    createdDate: '15/05/2025'
  },
  {
    id: 10,
    title: 'Frontend Modernization',
    description: 'Modernize the frontend framework from AngularJS to React.',
    status: 'Draft',
    createdDate: '14/05/2025'
  },
  {
    id: 11,
    title: 'API Gateway Implementation',
    description: 'Implement an API Gateway to manage microservices more efficiently.',
    status: 'Pending',
    createdDate: '10/05/2025'
  },
  {
    id: 12,
    title: 'Security Enhancement',
    description: 'Enhance security protocols and implement end-to-end encryption.',
    status: 'Approved',
    createdDate: '07/05/2025'
  },
  {
    id: 13,
    title: 'CI/CD Pipeline',
    description: 'Set up a Continuous Integration/Continuous Deployment pipeline for automated testing and deployment.',
    status: 'Denied',
    createdDate: '01/05/2025'
  },
  {
    id: 14,
    title: 'Monitoring System',
    description: 'Deploy a comprehensive monitoring system for real-time application performance tracking.',
    status: 'Pending',
    createdDate: '28/04/2025'
  },
  {
    id: 15,
    title: 'Data Analytics Platform',
    description: 'Build a data analytics platform to derive insights from user data.',
    status: 'Approved',
    createdDate: '25/04/2025'
  },
  {
    id: 16,
    title: 'Chatbot Integration',
    description: 'Integrate a chatbot to handle customer service inquiries.',
    status: 'Draft',
    createdDate: '20/04/2025'
  },
  {
    id: 17,
    title: 'Mobile App Development',
    description: 'Develop a mobile application for iOS and Android platforms.',
    status: 'Pending',
    createdDate: '18/04/2025'
  },
  {
    id: 18,
    title: 'Load Balancing',
    description: 'Implement load balancing to distribute network traffic across servers.',
    status: 'Approved',
    createdDate: '14/04/2025'
  },
  {
    id: 19,
    title: 'Disaster Recovery Plan',
    description: 'Create and implement a disaster recovery plan to ensure business continuity.',
    status: 'Denied',
    createdDate: '10/04/2025'
  },
  {
    id: 20,
    title: 'User Authentication System',
    description: 'Develop a robust user authentication system with multi-factor authentication.',
    status: 'Pending',
    createdDate: '05/04/2025'
  },
  {
    id: 21,
    title: 'Content Management System',
    description: 'Build a content management system for easy content updates and management.',
    status: 'Approved',
    createdDate: '01/04/2025'
  },
  {
    id: 22,
    title: 'Performance Optimization',
    description: 'Optimize application performance by reducing load times and improving responsiveness.',
    status: 'Draft',
    createdDate: '28/03/2025'
  },
  {
    id: 23,
    title: 'Third-Party Integration',
    description: 'Integrate third-party services for payment processing and email notifications.',
    status: 'Pending',
    createdDate: '25/03/2025'
  },
  {
    id: 24,
    title: 'Automated Backup System',
    description: 'Set up an automated backup system for critical data.',
    status: 'Approved',
    createdDate: '20/03/2025'
  },
  {
    id: 25,
    title: 'Scalability Improvements',
    description: 'Implement scalability improvements to handle increased user load.',
    status: 'Denied',
    createdDate: '15/03/2025'
  },
  {
    id: 26,
    title: 'Code Refactoring',
    description: 'Refactor legacy code to improve maintainability and performance.',
    status: 'Pending',
    createdDate: '10/03/2025'
  },
  {
    id: 27,
    title: 'User Feedback System',
    description: 'Develop a system for collecting and analyzing user feedback.',
    status: 'Approved',
    createdDate: '05/03/2025'
  },
  {
    id: 28,
    title: 'Accessibility Enhancements',
    description: 'Enhance application accessibility to comply with accessibility standards.',
    status: 'Draft',
    createdDate: '01/03/2025'
  },
  {
    id: 29,
    title: 'Multi-language Support',
    description: 'Add support for multiple languages to reach a global audience.',
    status: 'Pending',
    createdDate: '25/02/2025'
  },
  {
    id: 30,
    title: 'Real-time Notifications',
    description: 'Implement real-time notifications for user actions and updates.',
    status: 'Approved',
    createdDate: '20/02/2025'
  },
  {
    id: 31,
    title: 'Data Migration',
    description: 'Migrate data from legacy systems to new databases.',
    status: 'Denied',
    createdDate: '15/02/2025'
  },
  {
    id: 32,
    title: 'System Health Dashboard',
    description: 'Create a dashboard to monitor the health and status of various systems.',
    status: 'Pending',
    createdDate: '10/02/2025'
  },
  {
    id: 33,
    title: 'Automated Testing Framework',
    description: 'Develop an automated testing framework to ensure software quality.',
    status: 'Approved',
    createdDate: '05/02/2025'
  },
  {
    id: 34,
    title: 'User Onboarding Process',
    description: 'Design and implement a user onboarding process to improve user experience.',
    status: 'Draft',
    createdDate: '01/02/2025'
  },
  {
    id: 35,
    title: 'Security Audit',
    description: 'Conduct a security audit to identify and fix vulnerabilities.',
    status: 'Pending',
    createdDate: '25/01/2025'
  },
  {
    id: 36,
    title: 'Feature Flags System',
    description: 'Implement a feature flags system to control the release of new features.',
    status: 'Approved',
    createdDate: '20/01/2025'
  },
  {
    id: 37,
    title: 'Performance Monitoring',
    description: 'Set up performance monitoring to track application performance metrics.',
    status: 'Denied',
    createdDate: '15/01/2025'
  },
  {
    id: 38,
    title: 'Data Encryption',
    description: 'Implement data encryption to protect sensitive information.',
    status: 'Pending',
    createdDate: '10/01/2025'
  },
  {
    id: 39,
    title: 'API Documentation',
    description: 'Create comprehensive API documentation for developers.',
    status: 'Approved',
    createdDate: '05/01/2025'
  },
  {
    id: 40,
    title: 'User Interface Redesign',
    description: 'Redesign the user interface to improve usability and aesthetics.',
    status: 'Draft',
    createdDate: '01/01/2025'
  },
  {
    id: 41,
    title: 'Automated Deployment',
    description: 'Set up automated deployment pipelines for continuous delivery.',
    status: 'Pending',
    createdDate: '25/12/2024'
  },
  {
    id: 42,
    title: 'Error Tracking System',
    description: 'Implement an error tracking system to monitor and fix application errors.',
    status: 'Approved',
    createdDate: '20/12/2024'
  },
  {
    id: 43,
    title: 'Code Review Process',
    description: 'Establish a code review process to ensure code quality and consistency.',
    status: 'Denied',
    createdDate: '15/12/2024'
  },
  {
    id: 44,
    title: 'User Training Program',
    description: 'Develop a training program to educate users on new features and functionalities.',
    status: 'Pending',
    createdDate: '10/12/2024'
  },
  {
    id: 45,
    title: 'Data Visualization Tools',
    description: 'Integrate data visualization tools to present data insights effectively.',
    status: 'Approved',
    createdDate: '05/12/2024'
  },
  {
    id: 46,
    title: 'System Integration',
    description: 'Integrate various systems to streamline operations and improve efficiency.',
    status: 'Draft',
    createdDate: '01/12/2024'
  },
  {
    id: 47,
    title: 'Automated Reporting',
    description: 'Set up automated reporting to generate and distribute reports regularly.',
    status: 'Pending',
    createdDate: '25/11/2024'
  },
  {
    id: 48,
    title: 'User Access Management',
    description: 'Implement user access management to control and monitor user access.',
    status: 'Approved',
    createdDate: '20/11/2024'
  },
  {
    id: 49,
    title: 'System Performance Tuning',
    description: 'Tune system performance to optimize resource usage and efficiency.',
    status: 'Denied',
    createdDate: '15/11/2024'
  },
  {
    id: 50,
    title: 'Data Backup and Recovery',
    description: 'Implement data backup and recovery solutions to ensure data integrity.',
    status: 'Pending',
    createdDate: '10/11/2024'
  },
  {
    id: 51,
    title: 'Network Security Enhancements',
    description: 'Enhance network security to protect against cyber threats and attacks.',
    status: 'Approved',
    createdDate: '05/11/2024'
  },
  {
    id: 52,
    title: 'User Experience Improvements',
    description: 'Implement user experience improvements based on user feedback and analytics.',
    status: 'Draft',
    createdDate: '01/11/2024'
  },
  {
    id: 53,
    title: 'System Scalability Testing',
    description: 'Conduct scalability testing to ensure the system can handle increased load.',
    status: 'Pending',
    createdDate: '25/10/2024'
  },
  {
    id: 54,
    title: 'Data Privacy Compliance',
    description: 'Ensure data privacy compliance with relevant regulations and standards.',
    status: 'Approved',
    createdDate: '20/10/2024'
  },
  {
    id: 55,
    title: 'Automated User Provisioning',
    description: 'Set up automated user provisioning to streamline user management.',
    status: 'Denied',
    createdDate: '15/10/2024'
  },
  {
    id: 56,
    title: 'System Health Checks',
    description: 'Implement system health checks to monitor and maintain system health.',
    status: 'Pending',
    createdDate: '10/10/2024'
  },
  {
    id: 57,
    title: 'Data Migration Tool',
    description: 'Develop a data migration tool to facilitate seamless data migration.',
    status: 'Approved',
    createdDate: '05/10/2024'
  },
  {
    id: 58,
    title: 'User Activity Logging',
    description: 'Implement user activity logging to track and monitor user actions.',
    status: 'Draft',
    createdDate: '01/10/2024'
  },
  {
    id: 59,
    title: 'System Performance Monitoring',
    description: 'Set up system performance monitoring to track and optimize performance.',
    status: 'Pending',
    createdDate: '25/09/2024'
  },
  {
    id: 60,
    title: 'Data Encryption at Rest',
    description: 'Implement data encryption at rest to protect sensitive data.',
    status: 'Approved',
    createdDate: '20/09/2024'
  }
  ];

  // Filtered agent cards based on active tab
  get filteredAgentCards(): AgentCard[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;

    return this.getFilteredCards().slice(startIndex, endIndex);
  }

  // Get all filtered cards without pagination
  private getFilteredCards(): AgentCard[] {
    if (this.activeTab === 'all') {
      return this.allAgentCards;
    }

    const statusMap: { [key: string]: string } = {
      'approved': 'Approved',
      'pending': 'Pending',
      'denied': 'Denied',
      'draft': 'Draft'
    };

    const targetStatus = statusMap[this.activeTab];
    return this.allAgentCards.filter(card => card.status === targetStatus);
  }

  constructor(
    private tokenStorageService: TokenStorageService,
    private globalStoreService: GlobalStoreService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUserData();
    this.loadSelectedUser();
  }

  private loadUserData(): void {
    this.userName = this.tokenStorageService.getDaName() || 'Akash Kumar';
    this.userEmail = this.tokenStorageService.getDaUsername() || '<EMAIL>';
  }

  private loadSelectedUser(): void {
    this.globalStoreService.selectedUser.subscribe((user) => {
      this.selectedUser = user;
      if (user && user.role) {
        this.userRole = user.role;
      }
    });
  }

  onCreateAgent(): void {
   this.router.navigate(['/build/agents/individual']);
  }

  onViewAnalytics(): void {
    console.log('View Analytics clicked');
    // TODO: Implement navigation to analytics page
  }



  onEditAgent(agent: AgentCard): void {
    console.log('Edit agent:', agent);
    // TODO: Implement agent editing functionality
  }

  onViewAgent(agent: AgentCard): void {
    console.log('View agent:', agent);
    // TODO: Implement agent viewing functionality
  }

  onDeleteAgent(agent: AgentCard): void {
    console.log('Delete agent:', agent);
    // TODO: Implement agent deletion functionality
  }

  getStatusClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      'Approved': 'status-approved',
      'Pending': 'status-pending',
      'Denied': 'status-denied',
      'Draft': 'status-draft'
    };
    return statusClasses[status] || 'status-default';
  }

  get totalItemsForPagination(): number {
    return this.getFilteredCards().length;
  }

  get shouldShowPagination(): boolean {
    return this.totalItemsForPagination > this.itemsPerPage;
  }

  onPageChange(page: number): void {
    this.currentPage = page;
  }

  onTabChange(tabId: string): void {
    this.activeTab = tabId;
    this.currentPage = 1; // Reset to first page when changing tabs
  }
}
