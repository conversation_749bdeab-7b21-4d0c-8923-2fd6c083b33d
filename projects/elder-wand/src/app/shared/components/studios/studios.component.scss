.studios-container {
  padding: 0px 16px;

  .studios-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
    margin: 0;

    .studio-card {
      background: white;
      border-radius: 20px;
      overflow: hidden;
      cursor: pointer;
      transition:
        transform 0.3s ease,
        box-shadow 0.3s ease;
      border: 0.2px solid var(--Brand-Tertiary-500, #673ab7);
      box-shadow: var(--Elevation-01X) var(--Elevation-01Y)
        var(--Elevation-01Blur) var(--Elevation-01Spread)
        var(--BrandNeutraln-100);
      backdrop-filter: blur(24px);
      height: auto;

      &:hover {
        box-shadow: var(--Elevation-01X) var(--Elevation-01Y)
        var(--Elevation-01Blur) var(--Elevation-01Spread)
        var(--BrandNeutraln-100);
        transform: scale(1.03) translateY(-5px); // pop and lift effect
      }

      .card-content {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 24px 24px 0 24px;
        position: relative;

        .card-header {
          margin-bottom: 24px;

          h2 {
            color: #1d1d1d;
            font-family: Mulish;
            font-size: 32px;
            font-weight: 700;
            margin: 0 0 12px 0;
          }

          .description {
            color: #6b7280;
            font-family: Mulish;
            font-size: 16px;
            font-weight: 500;
            text-align: left;
            margin: 0;
          }
        }

        .card-body {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 20px;
        }

        .card-visual {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          img {
            width: 223px;
            height: 195px;
            object-fit: contain;
          }
        }

        .card-footer {
          position: fixed;
          bottom: 29px;
          right: 16px;
          display: flex;
          align-items: baseline;
          justify-content: center;

          .arrow-button {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: baseline !important;
            justify-content: center;
            border: 1px solid #e5e7eb;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .studios-grid {
    grid-template-columns: 1fr !important;
  }
}
