// Marketplace layout with sticky hero and tabs
.marketplace-container {
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: white;
}

// Fixed hero section
app-hero {
  position: sticky;
  top: 0;
  z-index: 4;
  flex-shrink: 0;
  background: white; // Ensure hero has background
}

// Agents component with sticky tabs and scrollable cards
app-agents {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; // Prevent overflow, let internal grid handle scrolling
}
