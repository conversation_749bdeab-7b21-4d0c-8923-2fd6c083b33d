import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { AgentsComponent } from '../../shared/components/agents/agents.component';
import { StudiosComponent } from '../../shared/components/studios/studios.component';
import Hero from '../../shared/components/hero/hero.component';
import { AnalyticsCardComponent } from '../../shared/components/analytics-card/analytics-card.component';
import { Agent } from '../../shared/interfaces/agent-list.interface';
import { EntityService } from '../../shared/services/entity.service';
import { RevelioSearchResult } from '../../shared/services/search.service';
import { GlobalStoreService } from '../../shared/service/global-store.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-launchpad-home',
  standalone: true,
  imports: [AgentsComponent, StudiosComponent, Hero, AnalyticsCardComponent],
  templateUrl: './launchpad-home.component.html',
  styleUrl: './launchpad-home.component.scss',
})
export class LaunchpadHomeComponent implements OnInit, OnDestroy {
  agentsData: Agent[] = [];
  searchResults: RevelioSearchResult[] = [];
  searchQuery: string = '';
  isSearchLoading: boolean = false;
  selectedStudioOption: string = 'EE';

  private studioSubscription: Subscription = new Subscription();

  constructor(
    private entityService: EntityService,
    private globalStoreService: GlobalStoreService,
  ) {}

  ngOnInit() {
    this.loadAgents();

    // Subscribe to studio option changes
    this.studioSubscription =
      this.globalStoreService.selectedStudioOption.subscribe(
        (option: string) => {
          this.selectedStudioOption = option;
        },
      );
  }

  ngOnDestroy() {
    this.studioSubscription.unsubscribe();
  }

  /**
   * Load agents and convert entity data to agent format
   */
  private loadAgents(): void {
    this.entityService.getAgents(0, 50).subscribe({
      next: (entityAgents) => {
        // Convert entity agents to the expected agent format
        this.agentsData = entityAgents.map((entityAgent) => ({
          id: entityAgent.id,
          title: entityAgent.name,
          description: entityAgent.description,
          rating: 4.5, // Default rating since it's not in the API
          studio: {
            name: 'Experience Studio', // Default studio
            type: 'Experience Studio',
            backgroundColor: '#FFF4F9',
            textColor: '#DC047B',
          },
          users: Math.floor(Math.random() * 100) + 10, // Random users count for now
        }));
      },
      error: (error) => {
        console.error('Error loading agents:', error);
        // Fallback to empty array
        this.agentsData = [];
      },
    });
  }

  /**
   * Handle search results from the search bar
   */
  onSearchResultsChange(results: RevelioSearchResult[]): void {
    this.searchResults = results;
  }

  /**
   * Handle search query change from the search bar
   */
  onSearchQueryChange(query: string): void {
    this.searchQuery = query;
  }

  /**
   * Handle search loading change from the search bar
   */
  onSearchLoadingChange(isLoading: boolean): void {
    this.isSearchLoading = isLoading;
  }

  /**
   * Handle send clicked from the search bar
   */
  onSendClicked(query: string): void {}
}
