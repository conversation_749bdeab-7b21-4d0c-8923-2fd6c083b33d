.launchpad-container {
  display: flex;
  gap: 12px;
  margin: 0 20px;
}

// Sidebar section
.sidebar-section {
  width: 80px;
  height: 947px;
  flex-shrink: 0;
}

// Main content section
.main-content-section {
  flex: 1;
  max-width: 1431px;
  height: calc(100vh - 90px);
  border-radius: 24px;
  background:
    linear-gradient(
      113.91deg,
      rgba(240, 235, 248, 0.8) 1.5%,
      rgba(255, 255, 255, 0.8) 50.17%,
      rgba(245, 233, 247, 0.8) 98.86%
    ),
    linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2));
  border: 2px solid var(--Global-colors-White-White, #ffffff);
  box-shadow: var(--Elevation-03set-2X) var(--Elevation-03set-2X)
    var(--Elevation-03set-2Blur) var(--Elevation-03set-2Spread)
    var(--Elevation-03set-2Color);
  box-shadow: var(--Elevation-03set-1X) 2px 2px var(--Elevation-03set-1Spread)
    var(--BrandNeutraln-50);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
}

// Analytics section
.analytics-section {
  width: 288px;
  height: calc(100vh - 90px);
  flex-shrink: 0;

  // Apply the same styling as main content
  display: flex;
  padding: 20px 10px 12px 10px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 24px;

  // Style with constant values (no CSS variables)
  border-radius: 24px;
  border: 2px solid #fff;
  background:
    linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.2) 100%
    ),
    linear-gradient(
      114deg,
      rgba(240, 235, 248, 0.8) 1.5%,
      rgba(255, 255, 255, 0.8) 50.17%,
      rgba(245, 233, 247, 0.8) 98.86%
    );
  box-shadow:
    0px 2px 2px -3px #f0f1f2,
    0px 0px 6px -2px #d1d3d8;
  backdrop-filter: blur(8px);

  .analytics-header {
    width: 100%;
    text-align: left;
    margin-bottom: 16px;

    h2 {
      color: #000;
      font-family: Mulish;
      font-size: 20px;
      font-weight: 600;
      margin: 0;
      margin-bottom: 10px;
    }
  }
}

// Content section styling
.content-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  // gap: 24px;

  .section-text {
    text-align: center;

    p {
      color: #616874;
      font-family: Mulish;
      font-size: 20px;
      font-weight: 600;
      line-height: 24px;
      margin: 0;
      margin-top: 18px;
      width: auto;
    }
  }

  .two-column-layout {
    display: flex;
    width: 100%;
    max-width: 1193px;
    height: 370px !important;
    gap: 8px;
    padding-top: 24px;

    .left-column {
      flex: 1;
      min-width: 0;
      // height: 100%;
      // overflow: hidden;
    }

    .right-column {
      flex: 2;
      min-width: 0;
      height: 100%;
      overflow: hidden;
      padding-right: 16px;
    }
  }
}

// Responsive adjustments
@media (max-width: 1400px) {
  .launchpad-container {
    gap: 24px; // Smaller gap on smaller screens
    margin: 0 10px;
  }

  .main-content-section {
    max-width: 1000px;
  }

  .analytics-section {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .launchpad-container {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .sidebar-section {
    width: 100%;
    height: auto;
  }

  .main-content-section,
  .analytics-section {
    width: 100%;
    height: auto;
    min-height: 400px;
    padding: 20px 10px;
  }

  .content-section {
    .two-column-layout {
      flex-direction: column;
      gap: 24px;
      height: auto;
    }

    .section-text p {
      font-size: 18px;
    }
  }
}

@media (max-width: 480px) {
  .launchpad-container {
    margin: 0 5px;
    gap: 12px;
  }

  .main-content-section,
  .analytics-section {
    padding: 15px 8px;
  }

  .content-section {
    .section-text p {
      font-size: 16px;
    }
  }
}
