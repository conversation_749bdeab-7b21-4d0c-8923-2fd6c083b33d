import {
  HeaderConfig,
  SharedNavItem,
} from '@shared/components/app-header/app-header.component';

// Elder Wand specific navigation items
const elderWandNavItems: SharedNavItem[] = [
  {
    label: 'Dashboard',
    route: '/',
    selected: true,
    hasDropdown: false,
    icon: `assets/icons/awe_dashboard.svg`,
  },
  {
    label: 'Agents',
    route: '/agents',
    selected: false,
    hasDropdown: true,
    dropdownOpen: false,
    icon: `assets/icons/awe_launch.svg`,
    dropdownItems: [
      {
        label: 'Create Agents',
        description: 'Build new agents',
        route: '/build/agents/individual',
        icon: `assets/icons/awe_agents.svg`,
      },
      {
        label: 'Create Workflows',
        description: 'Build new Workflows',
        route: '/build/workflows/create',
        icon: `assets/icons/awe_workflows.svg`,
      },
      {
        label: 'Create Tools',
        description: 'Create new Tools',
        route: '/libraries/tools/create',
        icon: `assets/icons/awe_tools.svg`,
      },
      {
        label: 'Create Guardrails',
        description: 'Create new Guardrails',
        route: '/libraries/guardrails/create',
        icon: `assets/icons/awe_guardrails.svg`,
      },
      {
        label: 'Create Knowledge Base',
        description: 'Create new Knowledge Base',
        route: '/libraries/knowledge-base/create',
        icon: `assets/icons/awe_knowledgebase.svg`,
      },
    ],
  },
  {
    label: 'Analytics',
    route: '/analytics',
    selected: false,
    hasDropdown: false,
    disabled: true,
    icon: `assets/icons/awe_analytics.svg`,
  },
  {
    label: 'My Agents',
    route: '/my-agent-home',
    selected: false,
    hasDropdown: false,
    icon: `assets/icons/profile.svg`,
  },
  {
    label: 'Marketplace',
    route: '/agent-list',
    selected: true,
    hasDropdown: false,
    icon: `assets/icons/awe_manage.svg`,
  },
];

// Available studio apps for the app drawer
const availableStudioApps = [
  {
    name: 'Elder Wand',
    route: '/dashboard',
    icon: 'header-ascendion-logo.svg',
    description: 'Central application launcher and hub',
  },
  {
    name: 'Experience Studio',
    route: 'https://aava-dev.avateam.io/experience/',
    icon: 'header-ascendion-logo.svg',
    description: 'AI-powered design analysis and code generation',
  },
  {
    name: 'Product Studio',
    route: 'http://localhost:4202/product/',
    icon: 'header-ascendion-logo.svg',
    description: 'Product strategy and business model canvas',
  },
  {
    name: 'Console',
    route: 'https://aava-dev.avateam.io/console/dashboard',
    icon: 'header-ascendion-logo.svg',
    description: 'Agent management and workflow automation',
  },
];

// Available languages for the language switcher
const availableLanguages = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
];

// Elder Wand header configuration
export const elderWandHeaderConfig: HeaderConfig = {
  logoSrc: 'assets/icons/header-ascendion-logo.svg',
  navItems: elderWandNavItems,
  showOrgSelector: true, // Elder Wand doesn't need org selector
  showThemeToggle: true,
  showAppDrawer: true,
  showProfileDropdown: true,
  showThemeToggleInProfile: true,
  showLanguageSwitcher: true,
  projectName: 'Elder Wand',
  redirectUrl: '/',
  currentApp: 'Elder Wand',
  availableApps: availableStudioApps,
  availableLanguages: availableLanguages,
  enableLogoAnimation: true,
  logoAnimationInterval: 4000, // 4 seconds between transitions
  logoAnimationStyle: 'fade',
  studioLogos: ['ascendion.svg', 'AAVA_logo.svg', 'LAUNCHPAD_LOGO.svg'],
  studioNames: [
    'Console Studio',
    'Experience Studio',
    'Product Studio',
    'Launchpad',
  ],
};
