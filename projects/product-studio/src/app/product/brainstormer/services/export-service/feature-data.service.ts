import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
  tenant?: string;
  justification?: string;
  moscow_rank?: string;
}

export interface FeatureSection {
  id: string;
  title: string; // 'Mo', 'S', 'Co', 'W'
  subtitle: string; // 'MUST HAVE', 'SHOULD HAVE', etc.
  features: FeatureCard[];
}

@Injectable({
  providedIn: 'root'
})
export class FeatureDataService {
  // Initialize with empty array - data will come from API
  private sectionsSubject = new BehaviorSubject<FeatureSection[]>([]);
  public sections$ = this.sectionsSubject.asObservable();

  constructor() {}

  // Get all sections
  getSections(): FeatureSection[] {
    return this.sectionsSubject.value;
  }

  // Get section by ID
  getSectionById(id: string): FeatureSection | undefined {
    return this.sectionsSubject.value.find(section => section.id === id);
  }

  // Get section IDs for drag-drop
  getSectionIds(): string[] {
    return this.sectionsSubject.value.map(section => section.id);
  }

  // Add new feature to a section
  addFeature(sectionId: string, feature: Omit<FeatureCard, 'id'>): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(section => section.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      const newFeature: FeatureCard = {
        ...feature,
        id: `${sectionId}-feature-${Date.now()}`
      };

      updatedSections[sectionIndex] = {
        ...updatedSections[sectionIndex],
        features: [...updatedSections[sectionIndex].features, newFeature]
      };

      this.sectionsSubject.next(updatedSections);
    }
  }

  // Update existing feature
  updateFeature(featureId: string, updatedFeature: Partial<FeatureCard>): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: [
            ...updatedSections[sectionIndex].features.slice(0, featureIndex),
            { ...updatedSections[sectionIndex].features[featureIndex], ...updatedFeature },
            ...updatedSections[sectionIndex].features.slice(featureIndex + 1)
          ]
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Delete feature
  deleteFeature(featureId: string): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: updatedSections[sectionIndex].features.filter(f => f.id !== featureId)
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Move feature between sections (for drag-drop)
  moveFeature(featureId: string, fromSectionId: string, toSectionId: string, newIndex: number): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    // Find and remove feature from source section
    const fromSectionIndex = updatedSections.findIndex(s => s.id === fromSectionId);
    const toSectionIndex = updatedSections.findIndex(s => s.id === toSectionId);

    if (fromSectionIndex !== -1 && toSectionIndex !== -1) {
      const featureIndex = updatedSections[fromSectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        const [movedFeature] = updatedSections[fromSectionIndex].features.splice(featureIndex, 1);
        updatedSections[toSectionIndex].features.splice(newIndex, 0, movedFeature);

        this.sectionsSubject.next(updatedSections);
      }
    }
  }

  // Reorder features within same section
  reorderFeatures(sectionId: string, fromIndex: number, toIndex: number): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      const features = [...updatedSections[sectionIndex].features];
      const [movedFeature] = features.splice(fromIndex, 1);
      features.splice(toIndex, 0, movedFeature);

      updatedSections[sectionIndex] = {
        ...updatedSections[sectionIndex],
        features
      };

      this.sectionsSubject.next(updatedSections);
    }
  }
  /**
   * Update features from API response
   * Maps API response structure to internal FeatureSection format
   */
  updateFromApiResponse(apiData: any): void {
    console.log('Processing Features API data:', apiData);

    const sections: FeatureSection[] = [
      {
        id: 'must-have',
        title: 'Mo',
        subtitle: 'MUST HAVE',
        features: this.mapApiFeaturesToCards(apiData.must_have || [], 'must')
      },
      {
        id: 'should-have',
        title: 'S',
        subtitle: 'SHOULD HAVE',
        features: this.mapApiFeaturesToCards(apiData.should_have || [], 'should')
      },
      {
        id: 'could-have',
        title: 'Co',
        subtitle: 'COULD HAVE',
        features: this.mapApiFeaturesToCards(apiData.could_have || [], 'could')
      },
      {
        id: 'wont-have',
        title: 'W',
        subtitle: "WON'T HAVE",
        features: this.mapApiFeaturesToCards(apiData.wont_have || [], 'wont')
      }
    ];

    console.log('Processed feature sections:', sections);
    this.sectionsSubject.next(sections);
  }

  /**
   * Map API feature items to internal FeatureCard format
   */
  private mapApiFeaturesToCards(apiFeatures: any[], prefix: string): FeatureCard[] {
    return apiFeatures.map((feature, index) => ({
      id: `${prefix}-${index + 1}`,
      title: feature.title || `Feature ${index + 1}`,
      description: feature.description || 'No description available',
      tags: feature.tags || [],
      tenant: feature.tenant || '',
      justification: feature.justification || '',
      moscow_rank: feature.moscow_rank || ''
    }));
  }
}