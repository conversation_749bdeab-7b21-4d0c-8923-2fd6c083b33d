/**
 * Utility functions for transforming roadmap data
 */

import { Epic, RoadmapProjectTask, JiraRequest } from '../interfaces/pipeline-api.interface';

/**
 * Transform project tasks array to Epic array for Jira export
 * @param projectTasks - Array of project tasks from roadmap API
 * @returns Array of Epic objects
 */
export function createEpicsFromProjectTasks(projectTasks: RoadmapProjectTask[]): Epic[] {
  if (!projectTasks || projectTasks.length === 0) {
    console.warn('⚠️ No project tasks provided for Epic transformation');
    return [];
  }

  return projectTasks.map((task, index) => {
    const epic: Epic = {
      id: (index + 1).toString(), // Use 1-based index as string
      title: task.task || `Task ${index + 1}`,
      description: task.long_description || task.description || 'No description available',
      additionalDetails: task.priority || 'medium'
    };

    console.log(`📋 Transformed task ${index + 1}:`, {
      original: task,
      epic: epic
    });

    return epic;
  });
}

/**
 * Create <PERSON>ra request payload from project tasks
 * @param projectTasks - Array of project tasks
 * @param appType - Application type (default: 'product-studio')
 * @param domain - Domain (default: 'roadmap-management')
 * @returns JiraRequest object
 */
export function createJiraPayload(
  projectTasks: RoadmapProjectTask[], 
  appType: string = 'product-studio', 
  domain: string = 'roadmap-management'
): JiraRequest {
  const epics = createEpicsFromProjectTasks(projectTasks);
  
  const jiraPayload: JiraRequest = {
    appType,
    domain,
    epics
  };

  console.log('📤 Created Jira payload:', jiraPayload);
  return jiraPayload;
}

/**
 * Example usage with your sample data
 */
export function exampleUsage() {
  // Your sample project_tasks data
  const sampleProjectTasks: RoadmapProjectTask[] = [
    {
      task: "Q1: UI/UX Design Phase (MVP)",
      description: "Design all user flows, wireframes, and high-fidelity mockups for the initial MVP launch, including the User Mobile App, Coffee Shop Owner Portal, and Internal Admin Dashboard.",
      long_description: "### Epic: MVP Design System & User Experience\n\n**Goal:** To create a comprehensive, intuitive, and visually appealing design for all core components of the platform required for a successful Q2 launch.\n\n**Scope:**\n- **User Mobile App (iOS & Android):**\n  - Onboarding and user profile creation.\n  - Home screen with mood/taste input for the Matching Engine.\n  - Search results view with map and list layouts.\n  - Advanced Search & Contextual Filters interface.\n  - Coffee Shop Profile page.\n  - Structured Review Submission flow.\n- **Coffee Shop Owner Portal (Web):**\n  - Business claiming and verification flow.\n  - Dashboard for managing profile information (hours, address, photos, amenities).\n- **Internal Admin Dashboard (Web):**\n  - Interface for Data Moderation & Curation.\n\n**Acceptance Criteria:**\n- All user flows are mapped and approved.\n- Wireframes for all screens are completed.\n- A high-fidelity, clickable prototype is available for stakeholder review.\n- A component library/design system is established for development.",
      priority: "high",
      duration: 40,
      quarter: 1
    }
  ];

  // Transform to epics
  const epics = createEpicsFromProjectTasks(sampleProjectTasks);
  console.log('📋 Transformed epics:', epics);

  // Create Jira payload
  const jiraPayload = createJiraPayload(sampleProjectTasks);
  console.log('📤 Jira payload:', jiraPayload);

  return { epics, jiraPayload };
}

/**
 * Validate Epic data
 * @param epics - Array of Epic objects to validate
 * @returns Validation result with details
 */
export function validateEpics(epics: Epic[]): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!epics || epics.length === 0) {
    errors.push('No epics provided');
    return { isValid: false, errors };
  }

  epics.forEach((epic, index) => {
    if (!epic.id || epic.id.trim().length === 0) {
      errors.push(`Epic ${index + 1}: Missing or empty ID`);
    }

    if (!epic.title || epic.title.trim().length === 0) {
      errors.push(`Epic ${index + 1}: Missing or empty title`);
    }

    if (!epic.description || epic.description.trim().length === 0) {
      errors.push(`Epic ${index + 1}: Missing or empty description`);
    }

    if (!epic.additionalDetails || epic.additionalDetails.trim().length === 0) {
      errors.push(`Epic ${index + 1}: Missing or empty additional details`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Format Epic description for Jira (clean up markdown)
 * @param description - Raw description text
 * @returns Formatted description
 */
export function formatEpicDescription(description: string): string {
  if (!description) return '';

  return description
    // Convert markdown headers to plain text
    .replace(/#{1,6}\s+/g, '')
    // Convert bold markdown to plain text
    .replace(/\*\*(.*?)\*\*/g, '$1')
    // Convert italic markdown to plain text
    .replace(/\*(.*?)\*/g, '$1')
    // Clean up excessive line breaks
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    // Trim whitespace
    .trim();
}

/**
 * Get summary statistics for Epic transformation
 * @param projectTasks - Original project tasks
 * @param epics - Transformed epics
 * @returns Summary statistics
 */
export function getTransformationSummary(
  projectTasks: RoadmapProjectTask[], 
  epics: Epic[]
): {
  totalTasks: number;
  totalEpics: number;
  priorityBreakdown: Record<string, number>;
  quarterBreakdown: Record<number, number>;
} {
  const priorityBreakdown: Record<string, number> = {};
  const quarterBreakdown: Record<number, number> = {};

  projectTasks.forEach(task => {
    // Count priorities
    const priority = task.priority || 'unknown';
    priorityBreakdown[priority] = (priorityBreakdown[priority] || 0) + 1;

    // Count quarters
    const quarter = task.quarter || 0;
    quarterBreakdown[quarter] = (quarterBreakdown[quarter] || 0) + 1;
  });

  return {
    totalTasks: projectTasks.length,
    totalEpics: epics.length,
    priorityBreakdown,
    quarterBreakdown
  };
}
