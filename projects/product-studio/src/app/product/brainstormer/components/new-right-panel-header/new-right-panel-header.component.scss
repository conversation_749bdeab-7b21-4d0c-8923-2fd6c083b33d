.right-panel-header {
  width: 100%;
  height: 54px;
  display: flex;
  align-items: center;
  background: linear-gradient(90deg, #8b5cf6 0%, #3c82f6 100%);
  border-radius: 12px;
  padding: 0 16px;

  // Theme support
  &[data-theme="dark"] {
    background: linear-gradient(90deg, #7c3aed 0%, #2563eb 100%);
  }

  .header-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    position: relative;
  }

  .header-left {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 0; // Allow text truncation

    .toggle-button {
      flex: 0 0 auto;
      width: 32px;
      height: 32px;
      border: none;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
        background: rgba(255, 255, 255, 0.25);
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.4);
      }

      svg {
        color: #ffffff;
      }
    }

    .title-container {
      flex: 1;
      min-width: 0; // Allow text truncation

      .project-title {
        color: #ffffff;
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        letter-spacing: 0.3px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .header-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none; // Allow clicks to pass through to underlying elements
  }

  .header-right {
    flex: 0 0 auto;
    display: flex;
    align-items: center;

    .step-pagination {
      display: flex;
      align-items: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 20px;
      padding: 4px 8px;
      backdrop-filter: blur(10px);

      .step-nav-button {
        width: 28px;
        height: 28px;
        border: none;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.35);
          transform: scale(1.05);
        }

        &:active:not(:disabled) {
          transform: scale(0.95);
        }

        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.4);
        }

        &:disabled {
          opacity: 0.4;
          cursor: not-allowed;
          background: rgba(255, 255, 255, 0.1);
        }

        svg {
          color: #ffffff;
          width: 14px;
          height: 14px;
        }
      }

      .step-counter {
        display: flex;
        align-items: center;
        gap: 2px;
        padding: 0 8px;
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

        .current-step {
          color: #ffffff;
        }

        .step-separator {
          color: rgba(255, 255, 255, 0.7);
          margin: 0 2px;
        }

        .total-steps {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .right-panel-header {
    height: 48px;
    padding: 0 12px;

    .header-content {
      gap: 12px;
    }

    .header-left {
      gap: 8px;

      .toggle-button {
        width: 28px;
        height: 28px;

        svg {
          width: 14px;
          height: 14px;
        }
      }

      .title-container .project-title {
        font-size: 14px;
      }
    }

    .header-right .step-pagination {
      gap: 6px;
      padding: 3px 6px;

      .step-nav-button {
        width: 26px;
        height: 26px;

        svg {
          width: 12px;
          height: 12px;
        }
      }

      .step-counter {
        padding: 0 6px;
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 480px) {
  .right-panel-header {
    height: 44px;
    padding: 0 8px;

    .header-content {
      gap: 8px;
    }

    .header-left {
      gap: 6px;

      .toggle-button {
        width: 26px;
        height: 26px;

        svg {
          width: 12px;
          height: 12px;
        }
      }

      .title-container .project-title {
        font-size: 13px;
      }
    }

    .header-right .step-pagination {
      gap: 4px;
      padding: 2px 4px;

      .step-nav-button {
        width: 24px;
        height: 24px;

        svg {
          width: 10px;
          height: 10px;
        }
      }

      .step-counter {
        padding: 0 4px;
        font-size: 12px;
      }
    }
  }
}

// Loading state styles
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  gap: 8px;

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.7) 25%,
      rgba(255, 255, 255, 1) 50%,
      rgba(255, 255, 255, 0.7) 75%
    );
    background-size: 200% 100%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: minimal-text-shine 1.5s ease-in-out infinite;
  }
}

// Next button loading state
.step-nav-button.loading {
  opacity: 0.6;
  cursor: not-allowed;

  &:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: none !important;
  }
}

// Spinner animation
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Shining gradient animation (same as Experience Studio)
@keyframes minimal-text-shine {
  0% {
    background-position: 200%;
  }
  100% {
    background-position: 0;
  }
}

// Responsive loading text and spinner
@media (max-width: 768px) {
  .loading-container {
    gap: 6px;

    .loading-spinner {
      width: 14px;
      height: 14px;
    }

    .loading-text {
      font-size: 12px;
    }
  }
}

@media (max-width: 480px) {
  .loading-container {
    gap: 4px;

    .loading-spinner {
      width: 12px;
      height: 12px;
    }

    .loading-text {
      font-size: 11px;
    }
  }
}
