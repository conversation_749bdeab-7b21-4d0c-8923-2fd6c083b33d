.custom-feature-list-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.feature-list-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

// Custom Dropdown Styles
.custom-dropdown {
  position: relative;
  z-index: 10;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  min-width: 140px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #cbd5e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  &.active {
    border-color: #0F9D57;
    box-shadow: 0 0 0 3px rgba(15, 157, 87, 0.1);
  }
}

.dropdown-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.filter-label {
  color: #4a5568;
  font-weight: 500;
}

.dropdown-arrow {
  color: #718096;
  transition: transform 0.2s ease;
  
  &.rotated {
    transform: rotate(180deg);
  }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  min-width: 160px;
  z-index: 50;
  margin-top: 0.25rem;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f7fafc;
  }

  &.selected {
    background-color: #edf2f7;
    
    .filter-label {
      font-weight: 600;
    }
  }

  &:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  &:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}

// Feature Cards Grid
.feature-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  flex: 1;
}

.feature-card {
  background: #f8f9fa;
  border-radius: 8px;
  border-top: 3px solid #0F9D57;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  min-height: 50px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.feature-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #2d3748;
  line-height: 1.4;
  word-break: break-word;
}

// Empty State
.empty-state {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.empty-message {
  color: #718096;
  font-style: italic;
  font-size: 0.875rem;
}

// Responsive Design
@media (max-width: 768px) {
  .feature-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .custom-dropdown {
    align-self: flex-end;
  }

  .feature-cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .feature-card {
    padding: 0.5rem 0.75rem;
    min-height: 45px;
  }

  .feature-name {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .feature-cards-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .dropdown-toggle {
    min-width: 120px;
    padding: 0.4rem 0.75rem;
  }
}
