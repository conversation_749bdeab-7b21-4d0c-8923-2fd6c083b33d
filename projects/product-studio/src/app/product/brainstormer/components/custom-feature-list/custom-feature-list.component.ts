import { Component, Input, OnInit, OnDestroy, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';

export interface CustomFeature {
  name: string;
  color: string;
  category: 'must_have' | 'should_have' | 'could_have' | 'wont_have';
}

export interface FilterOption {
  value: 'all' | 'must_have' | 'should_have' | 'could_have' | 'wont_have';
  label: string;
  color: string;
}

@Component({
  selector: 'app-custom-feature-list',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './custom-feature-list.component.html',
  styleUrl: './custom-feature-list.component.scss'
})
export class CustomFeatureListComponent implements OnInit, OnDestroy, OnChanges {
  @Input() features: CustomFeature[] = [];
  @Input() showDropdown: boolean = true;

  filteredFeatures: CustomFeature[] = [];
  selectedFilter: FilterOption['value'] = 'must_have'; // Default to "Must Have"
  isDropdownOpen: boolean = false;
  private subscriptions: Subscription[] = [];

  filterOptions: FilterOption[] = [
    { value: 'all', label: 'All Features', color: '#6c757d' },
    { value: 'must_have', label: 'Must Have', color: '#0F9D57' },
    { value: 'should_have', label: 'Should Have', color: '#FDC100' },
    { value: 'could_have', label: 'Could Have', color: '#FD7542' },
    { value: 'wont_have', label: 'Won\'t Have', color: '#25364D' }
  ];

  ngOnInit(): void {
    this.applyFilter();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  ngOnChanges(): void {
    this.applyFilter();
  }

  applyFilter(): void {
    if (this.selectedFilter === 'all') {
      this.filteredFeatures = [...this.features];
    } else {
      this.filteredFeatures = this.features.filter(feature => feature.category === this.selectedFilter);
    }
  }

  selectFilter(filterValue: FilterOption['value']): void {
    this.selectedFilter = filterValue;
    this.isDropdownOpen = false;
    this.applyFilter();
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  getSelectedFilterOption(): FilterOption {
    return this.filterOptions.find(option => option.value === this.selectedFilter) || this.filterOptions[1];
  }

  // Handle click outside dropdown to close it
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = document.querySelector('.custom-dropdown');
    if (dropdown && !dropdown.contains(target)) {
      this.closeDropdown();
    }
  }

  // TrackBy function for performance optimization
  trackByFeature(index: number, feature: CustomFeature): string {
    return `${feature.name}-${feature.category}`;
  }
}
