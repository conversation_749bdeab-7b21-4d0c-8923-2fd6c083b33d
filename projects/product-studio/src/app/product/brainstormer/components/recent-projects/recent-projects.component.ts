import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { GetRecentProjectsService, ProjectListItem } from '../../services/get-recent-projects.service';

@Component({
  selector: 'app-recent-projects',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './recent-projects.component.html',
  styleUrl: './recent-projects.component.scss'
})
export class RecentProjectsComponent implements OnInit, OnDestroy {
  projects: ProjectListItem[] = [];
  isLoading = false;
  error: string | null = null;
  loadingProjectId: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private recentProjectsService: GetRecentProjectsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadRecentProjects();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load recent projects from API
   */
  loadRecentProjects(): void {
    this.isLoading = true;
    this.error = null;

    console.log('🔄 Loading recent projects...');

    this.recentProjectsService.getRecentProjects()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projects) => {
          this.projects = projects;
          this.isLoading = false;
          console.log(`✅ Loaded ${projects.length} recent projects`);
        },
        error: (error) => {
          this.error = 'Failed to load recent projects. Please try again.';
          this.isLoading = false;
          console.error('❌ Failed to load recent projects:', error);
        }
      });
  }

  /**
   * Handle project card click - load project and navigate to brainstorming
   * @param project - The project to load
   */
  onProjectClick(project: ProjectListItem): void {
    if (this.loadingProjectId) {
      console.log('⚠️ Another project is already loading');
      return;
    }

    this.loadingProjectId = project.id;
    console.log(`🔄 Loading project: ${project.name} (${project.run_id})`);

    this.recentProjectsService.loadProject(project.run_id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projectDetails) => {
          this.loadingProjectId = null;
          console.log(`✅ Project loaded successfully: ${projectDetails.name}`);
          
          // Navigate to brainstorming interface with loaded project data
          this.router.navigate(['/brainstormer/brainstorming']).then(() => {
            console.log('🧭 Navigated to brainstorming interface');
          });
        },
        error: (error) => {
          this.loadingProjectId = null;
          this.error = `Failed to load project "${project.name}". Please try again.`;
          console.error(`❌ Failed to load project ${project.name}:`, error);
        }
      });
  }

  /**
   * Retry loading recent projects
   */
  retry(): void {
    this.loadRecentProjects();
  }

  /**
   * Check if a specific project is currently loading
   * @param projectId - The project ID to check
   * @returns boolean indicating if project is loading
   */
  isProjectLoading(projectId: string): boolean {
    return this.loadingProjectId === projectId;
  }

  /**
   * Format date for display
   * @param dateString - ISO date string
   * @returns Formatted date string
   */
  formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Invalid Date';
    }
  }

  /**
   * Get status badge class for styling
   * @param status - Project status
   * @returns CSS class name
   */
  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'status-completed';
      case 'in_progress':
        return 'status-in-progress';
      case 'draft':
        return 'status-draft';
      default:
        return 'status-default';
    }
  }

  /**
   * Get display text for status
   * @param status - Project status
   * @returns Display text
   */
  getStatusDisplayText(status: string): string {
    switch (status.toLowerCase()) {
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'draft':
        return 'Draft';
      default:
        return status;
    }
  }

  /**
   * Track by function for ngFor performance
   * @param index - Array index
   * @param project - Project item
   * @returns Unique identifier
   */
  trackByProjectId(index: number, project: ProjectListItem): string {
    return project.id;
  }
}
