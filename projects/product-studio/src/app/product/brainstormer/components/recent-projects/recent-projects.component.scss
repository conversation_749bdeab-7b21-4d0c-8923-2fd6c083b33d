.recent-projects-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    .title {
      font-size: 2rem;
      font-weight: 700;
      color: #1f2937;
      margin: 0;
    }

    .refresh-btn {
      background: none;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      padding: 8px 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        border-color: #7c3aed;
        background-color: #f3f4f6;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .refresh-icon {
        font-size: 1.2rem;
        display: inline-block;
        transition: transform 0.5s ease;

        &.spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e5e7eb;
      border-top: 4px solid #7c3aed;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    .loading-text {
      color: #6b7280;
      font-size: 1.1rem;
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;

    .error-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .error-message {
      color: #dc2626;
      font-size: 1.1rem;
      margin-bottom: 1.5rem;
    }

    .retry-btn {
      background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;

    .project-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      padding: 1.5rem;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover:not(.loading) {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border-color: #7c3aed;

        .click-indicator .arrow {
          transform: translateX(4px);
        }
      }

      &.loading {
        pointer-events: none;
        opacity: 0.7;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;

        .project-name {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
          flex: 1;
          margin-right: 1rem;
        }

        .status-badge {
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.status-completed {
            background-color: #d1fae5;
            color: #065f46;
          }

          &.status-in-progress {
            background-color: #dbeafe;
            color: #1e40af;
          }

          &.status-draft {
            background-color: #fef3c7;
            color: #92400e;
          }

          &.status-default {
            background-color: #f3f4f6;
            color: #374151;
          }
        }
      }

      .project-description {
        color: #6b7280;
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 1.5rem;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .project-metadata {
        margin-bottom: 1rem;

        .metadata-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.5rem;
          font-size: 0.875rem;

          .label {
            color: #6b7280;
            font-weight: 500;
          }

          .value {
            color: #374151;
          }
        }
      }

      .project-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;

        .tag {
          background-color: #f3f4f6;
          color: #374151;
          padding: 4px 8px;
          border-radius: 6px;
          font-size: 0.75rem;
          font-weight: 500;
        }
      }

      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .loading-spinner {
          width: 24px;
          height: 24px;
          border: 2px solid #e5e7eb;
          border-top: 2px solid #7c3aed;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 0.5rem;
        }

        .loading-message {
          color: #6b7280;
          font-size: 0.875rem;
          font-weight: 500;
        }
      }

      .click-indicator {
        position: absolute;
        top: 1rem;
        right: 1rem;
        opacity: 0;
        transition: opacity 0.3s ease;

        .arrow {
          color: #7c3aed;
          font-size: 1.2rem;
          font-weight: bold;
          transition: transform 0.3s ease;
        }
      }

      &:hover .click-indicator {
        opacity: 1;
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    .empty-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
    }

    .empty-message {
      color: #6b7280;
      font-size: 1rem;
      margin-bottom: 2rem;
      max-width: 400px;
    }

    .create-project-btn {
      background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .recent-projects-container {
    padding: 1rem;

    .header .title {
      font-size: 1.5rem;
    }

    .projects-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .project-card {
      padding: 1rem;
    }
  }
}
