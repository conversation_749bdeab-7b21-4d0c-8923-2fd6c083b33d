import { TestBed } from '@angular/core/testing';
import { BehaviorSubject } from 'rxjs';
import { SummaryService } from './summary.service';
import { AppStateService } from '../../../shared/services/app-state.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { PersonaDataService } from '../../services/persona-data.service';
import { RoadmapDataService } from '../../services/roadmap-data.service';
import { SummaryData } from './summary.interfaces';

describe('SummaryService Session Storage', () => {
  let service: SummaryService;
  let mockAppStateService: jasmine.SpyObj<AppStateService>;
  let mockRoadmapDataService: jasmine.SpyObj<RoadmapDataService>;

  const mockSummaryData: SummaryData = {
    name: 'Test Project',
    description: 'Test Description',
    progress: 100,
    hasData: true,
    features: [
      { id: '1', name: 'Feature 1', description: 'Test feature', color: '#FF0000' }
    ],
    personas: [
      { id: '1', name: 'Test Persona', description: 'Test persona description', color: '#00FF00' }
    ],
    swot: [
      { id: '1', category: 'strengths', text: 'Test strength', color: '#0000FF' }
    ],
    timeline: [
      { id: 1, name: 'Q1', tasks: [], color: '#FFFF00' }
    ],
    contributionText: 'Test contribution'
  };

  beforeEach(() => {
    const appStateServiceSpy = jasmine.createSpyObj('AppStateService', ['updatePipelineState'], {
      pipelineState$: new BehaviorSubject({
        run_id: 'test-run-id',
        project_name: 'Test Project',
        data: {},
        current_step: 'roadmap'
      }),
      loadingState$: new BehaviorSubject({ isLoadingPipeline: false })
    });

    const roadmapDataServiceSpy = jasmine.createSpyObj('RoadmapDataService', ['getEpicsFromRoadmap'], {
      originalApiData$: new BehaviorSubject(null)
    });

    TestBed.configureTestingModule({
      providers: [
        SummaryService,
        { provide: AppStateService, useValue: appStateServiceSpy },
        { provide: ProductPipelineService, useValue: {} },
        { provide: PersonaDataService, useValue: {} },
        { provide: RoadmapDataService, useValue: roadmapDataServiceSpy }
      ]
    });

    service = TestBed.inject(SummaryService);
    mockAppStateService = TestBed.inject(AppStateService) as jasmine.SpyObj<AppStateService>;
    mockRoadmapDataService = TestBed.inject(RoadmapDataService) as jasmine.SpyObj<RoadmapDataService>;

    // Clear session storage before each test
    sessionStorage.clear();
  });

  afterEach(() => {
    // Clean up session storage after each test
    sessionStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should save summary data to session storage', () => {
    // Call the private method through the public interface
    service.updateSessionStorage();

    // Check if data was saved to session storage
    const storedData = sessionStorage.getItem('summary_data');
    expect(storedData).toBeTruthy();

    if (storedData) {
      const parsedData = JSON.parse(storedData);
      expect(parsedData.timestamp).toBeTruthy();
    }
  });

  it('should load summary data from session storage', () => {
    // Manually save test data to session storage
    const testDataWithTimestamp = {
      ...mockSummaryData,
      timestamp: new Date().toISOString()
    };
    sessionStorage.setItem('summary_data', JSON.stringify(testDataWithTimestamp));

    // Get data from session storage
    const retrievedData = service.getSummaryDataFromStorage();

    expect(retrievedData).toBeTruthy();
    expect(retrievedData?.name).toBe('Test Project');
    expect(retrievedData?.hasData).toBe(true);
  });

  it('should return null for non-existent session storage data', () => {
    const retrievedData = service.getSummaryDataFromStorage();
    expect(retrievedData).toBeNull();
  });

  it('should handle corrupted session storage data gracefully', () => {
    // Save corrupted data to session storage
    sessionStorage.setItem('summary_data', 'invalid-json');

    const retrievedData = service.getSummaryDataFromStorage();
    expect(retrievedData).toBeNull();

    // Verify that corrupted data was cleared
    const storedData = sessionStorage.getItem('summary_data');
    expect(storedData).toBeNull();
  });

  it('should reject stale session storage data', () => {
    // Create data that's 2 hours old
    const staleTimestamp = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString();
    const staleData = {
      ...mockSummaryData,
      timestamp: staleTimestamp
    };
    sessionStorage.setItem('summary_data', JSON.stringify(staleData));

    const retrievedData = service.getSummaryDataFromStorage();
    expect(retrievedData).toBeNull();

    // Verify that stale data was cleared
    const storedData = sessionStorage.getItem('summary_data');
    expect(storedData).toBeNull();
  });

  it('should accept recent session storage data', () => {
    // Create data that's 30 minutes old (within 1 hour limit)
    const recentTimestamp = new Date(Date.now() - 30 * 60 * 1000).toISOString();
    const recentData = {
      ...mockSummaryData,
      timestamp: recentTimestamp
    };
    sessionStorage.setItem('summary_data', JSON.stringify(recentData));

    const retrievedData = service.getSummaryDataFromStorage();
    expect(retrievedData).toBeTruthy();
    expect(retrievedData?.name).toBe('Test Project');
  });

  it('should setup roadmap data subscription', () => {
    // Verify that the service subscribes to roadmap data changes
    expect(mockRoadmapDataService.originalApiData$).toBeTruthy();
    
    // Simulate roadmap data update
    const mockRoadmapData = {
      project_tasks: [
        {
          task: 'Test Task',
          description: 'Test Description',
          priority: 'high',
          duration: 30,
          quarter: 1
        }
      ]
    };

    // This should trigger the subscription and update summary data
    (mockRoadmapDataService.originalApiData$ as BehaviorSubject<any>).next(mockRoadmapData);

    // The subscription should be active (we can't easily test the callback without more setup)
    expect(service).toBeTruthy();
  });

  it('should handle session storage quota exceeded error', () => {
    // Mock sessionStorage.setItem to throw quota exceeded error
    spyOn(Storage.prototype, 'setItem').and.throwError('QuotaExceededError');

    // This should not throw an error
    expect(() => service.updateSessionStorage()).not.toThrow();
  });

  it('should clean up subscriptions on destroy', () => {
    // Create the service and then destroy it
    service.ngOnDestroy();

    // The service should handle cleanup gracefully
    expect(service).toBeTruthy();
  });
});
