# Recent Projects Data Loading Debug Guide

## Overview
This guide helps debug the project data loading issue where project names and feature data show as undefined after selecting a project from the recent projects list.

## Enhanced Debugging Features Added

### 1. Recent Projects Component Debugging
**File:** `recent-projects.component.ts`

**Enhanced Logging:**
```typescript
// In loadSelectedProject method:
console.log(`🔄 RecentProjectsComponent: Loading selected project: ${project.name}`);
console.log(`🔄 RecentProjectsComponent: Project run_id: ${project.run_id}`);
console.log(`🔄 RecentProjectsComponent: Full project object:`, project);
console.log(`✅ RecentProjectsComponent: Project loaded successfully: ${projectDetails.name}`);
console.log(`📊 RecentProjectsComponent: Full project details received:`, projectDetails);
```

### 2. GetRecentProjectsService Debugging
**File:** `get-recent-projects.service.ts`

**Enhanced Logging:**
```typescript
// In loadProject method:
console.log('🔄 GetRecentProjectsService: Starting data transformation');
console.log('📊 GetRecentProjectsService: Raw project details from API:', projectDetails);
console.log('🔄 GetRecentProjectsService: Transformed pipeline state:', pipelineState);
console.log('📋 GetRecentProjectsService: Project name in transformed state:', pipelineState.project_name);
console.log('📋 GetRecentProjectsService: Features data in transformed state:', pipelineState.data?.features);

// In transformProjectDetailsToPipelineState method:
console.log('📊 Input details object:', details);
console.log('📋 Project name from API:', details.name);
console.log('📋 Features data from API:', details.features);
console.log('✅ Transformation complete. Result:', transformedState);
```

### 3. Brainstorming Component Debugging
**File:** `brainstorming.component.ts`

**Enhanced Logging:**
```typescript
// In ngOnInit:
console.log('🔄 BrainstormingComponent: ngOnInit started');
console.log('📊 BrainstormingComponent: Current pipeline state on init:', currentPipelineState);
console.log('📋 BrainstormingComponent: Project name on init:', currentPipelineState.project_name);
console.log('📋 BrainstormingComponent: Features data on init:', currentPipelineState.data.features);

// In pipeline state subscription:
console.log('📊 BrainstormingComponent: Pipeline state updated:', pipelineState);
console.log('📋 BrainstormingComponent: Project name in state:', pipelineState.project_name);
console.log('📋 BrainstormingComponent: Features in state:', pipelineState.data.features);
```

### 4. Feature List Component Debugging
**File:** `feature-list.component.ts`

**Enhanced Logging:**
```typescript
// In ngOnInit:
console.log('🔄 FeatureListComponent: ngOnInit started');
console.log('📊 FeatureListComponent: Current pipeline state on init:', currentState);
console.log('📋 FeatureListComponent: Features data on init:', currentState.data.features);

// In pipeline state subscription:
console.log('📊 FeatureListComponent: Pipeline state updated:', state);
console.log('📋 FeatureListComponent: Project name in state:', state.project_name);
console.log('📋 FeatureListComponent: Features data in state:', state.data.features);
```

### 5. Debug State Component
**File:** `debug-state.component.ts`

**Visual Debug Panel:**
- Fixed position panel showing real-time state data
- Displays App State Service data vs Pipeline Service data
- Shows raw JSON state for inspection
- Refresh button to manually update debug data

## Testing Steps

### Step 1: Open Browser Developer Tools
1. Open Chrome/Firefox Developer Tools (F12)
2. Go to Console tab
3. Clear console for clean output

### Step 2: Navigate to Dashboard
1. Go to the main dashboard
2. Wait for recent projects to load
3. Check console for loading messages:
   ```
   🔄 RecentProjectsComponent: Loading projects from API
   ✅ RecentProjectsComponent: Loaded X projects from API
   ```

### Step 3: Select a Project
1. Click on any project card in the recent projects section
2. Monitor console output for the following sequence:
   ```
   🔄 RecentProjectsComponent: Loading selected project: [Project Name]
   🔄 RecentProjectsComponent: Project run_id: [Run ID]
   🔄 GetRecentProjectsService: Starting data transformation
   📊 GetRecentProjectsService: Raw project details from API: [API Response]
   🔄 GetRecentProjectsService: Transformed pipeline state: [Transformed Data]
   ✅ GetRecentProjectsService: Project data loaded into pipeline state
   🧭 RecentProjectsComponent: Navigating to brainstorming interface
   ```

### Step 4: Check Brainstorming Component
1. After navigation, check console for:
   ```
   🔄 BrainstormingComponent: ngOnInit started
   📊 BrainstormingComponent: Current pipeline state on init: [State Data]
   📋 BrainstormingComponent: Project name on init: [Project Name or undefined]
   📋 BrainstormingComponent: Features data on init: [Features or undefined]
   ```

### Step 5: Use Debug Panel
1. Look for the red debug panel in the top-right corner
2. Check the displayed values:
   - **App State Service Project Name:** Should show the loaded project name
   - **Pipeline Service Project Name:** Should match App State Service
   - **Features Available:** Should show "Yes" if features data exists
3. Click "Refresh Debug Data" to update values
4. Inspect the raw JSON state for detailed data structure

## Common Issues to Look For

### Issue 1: API Response Problems
**Symptoms:**
- Console shows API errors
- Raw project details are empty or malformed

**Debug Steps:**
1. Check Network tab in Developer Tools
2. Look for failed API requests to `/project/details`
3. Verify the request payload contains correct `run_id`
4. Check response status and data structure

### Issue 2: Data Transformation Problems
**Symptoms:**
- API response is successful but transformed state is incorrect
- Project name or features are undefined in transformed state

**Debug Steps:**
1. Compare raw API response with transformed pipeline state in console
2. Check if API response fields match expected structure:
   ```json
   {
     "name": "Project Name",
     "features": { ... },
     "run_id": "...",
     "metadata": { ... }
   }
   ```

### Issue 3: State Synchronization Problems
**Symptoms:**
- Data loads correctly but components show undefined values
- Debug panel shows different values between App State and Pipeline Service

**Debug Steps:**
1. Check if both services are receiving the same data
2. Verify observable subscriptions are working
3. Look for timing issues (data loaded after component initialization)

### Issue 4: Component Subscription Problems
**Symptoms:**
- State is correct but UI components don't update
- Debug panel shows correct data but feature list is empty

**Debug Steps:**
1. Check if components are subscribing to correct observables
2. Verify change detection is triggered
3. Look for subscription cleanup issues

## Expected Console Output (Success Case)

```
🔄 RecentProjectsComponent: Loading selected project: My Test Project
🔄 RecentProjectsService: Project run_id: run-12345
📊 RecentProjectsComponent: Full project object: {id: "...", name: "My Test Project", ...}
🔄 GetRecentProjectsService: Starting data transformation
📊 GetRecentProjectsService: Raw project details from API: {name: "My Test Project", features: {...}, ...}
🔄 GetRecentProjectsService: Transformed pipeline state: {project_name: "My Test Project", data: {features: {...}}, ...}
📋 GetRecentProjectsService: Project name in transformed state: My Test Project
📋 GetRecentProjectsService: Features data in transformed state: {...}
✅ GetRecentProjectsService: Project data loaded into pipeline state
🔍 GetRecentProjectsService: Current app state after update: {project_name: "My Test Project", ...}
✅ RecentProjectsComponent: Project loaded successfully: My Test Project
🧭 RecentProjectsComponent: Navigating to brainstorming interface
🔄 BrainstormingComponent: ngOnInit started
📊 BrainstormingComponent: Current pipeline state on init: {project_name: "My Test Project", ...}
📋 BrainstormingComponent: Project name on init: My Test Project
📋 BrainstormingComponent: Features data on init: {...}
🔄 FeatureListComponent: ngOnInit started
📊 FeatureListComponent: Current pipeline state on init: {project_name: "My Test Project", ...}
📋 FeatureListComponent: Features data on init: {...}
✅ FeatureListComponent: Loading features data from API response: {...}
```

## Cleanup Instructions

After debugging is complete, remove the debug components:

1. **Remove Debug Panel:**
   - Remove `<app-debug-state></app-debug-state>` from `brainstorming.component.html`
   - Remove `DebugStateComponent` import and from imports array in `brainstorming.component.ts`
   - Delete `debug-state.component.ts` file

2. **Reduce Console Logging:**
   - Remove or comment out excessive console.log statements
   - Keep essential error logging and key milestone logs

3. **Remove Navigation Delay:**
   - Remove the 500ms setTimeout in `loadSelectedProject` method
   - Restore immediate navigation after successful data loading

This debugging setup will help identify exactly where the data flow breaks and why project names and features are showing as undefined.
