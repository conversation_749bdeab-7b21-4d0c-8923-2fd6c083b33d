import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { PipelineStep } from '../../../brainstormer/interfaces/pipeline-api.interface';
import { AppStateService } from '../app-state.service';

export interface StepperStep {
  id: string;
  label: string;
  state: 'completed' | 'active' | 'inactive' | 'loading';
  component?: string;
  route?: string;
  icon?: string;
  hasData?: boolean; // Track if step has API data
  isLoading?: boolean; // Track if step is currently loading
  apiStep?: string; // The corresponding API step name
  // description?: string;
}

@Injectable({
  providedIn: 'root'
})
export class StepperService implements OnDestroy {
  private readonly steps: StepperStep[] = [
    {
      id: 'understanding',
      label: 'Understanding',
      state: 'active',
      component: 'understanding',
      route: '/product-studio/brainstormer/understanding',
      apiStep: 'market_research',
      isLoading: false,
      hasData: false
    },
    {
      id: 'persona',
      label: 'User Persona',
      state: 'inactive',
      component: 'persona',
      route: '/product-studio/brainstormer/persona',
      apiStep: 'lbc',
      isLoading: false,
      hasData: false
    },
    {
      id: 'swot',
      label: 'SWOT Analysis',
      state: 'inactive',
      component: 'swot',
      route: '/product-studio/brainstormer/swot',
      apiStep: 'persona',
      isLoading: false,
      hasData: false
    },
    {
      id: 'features',
      label: 'Feature List',
      state: 'inactive',
      component: 'features',
      route: '/product-studio/brainstormer/feature-list',
      apiStep: 'swot',
      isLoading: false,
      hasData: false
    },
    {
      id: 'roadmap',
      label: 'Product Roadmap',
      state: 'inactive',
      component: 'roadmap',
      route: '/product-studio/brainstormer/roadmap',
      apiStep: 'features',
      isLoading: false,
      hasData: false
    }
  ];

  private currentStepIndexSubject = new BehaviorSubject<number>(0);
  private stepsSubject = new BehaviorSubject<StepperStep[]>([...this.steps]);
  private subscriptions: Subscription[] = [];

  constructor(private appStateService: AppStateService) {
    this.updateStepStates();
    this.setupStateSubscriptions();

    // Initialize pipeline state with current step when stepper is created
    this.initializePipelineState();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Setup subscriptions to centralized state changes
   */
  private setupStateSubscriptions(): void {
    // Subscribe to pipeline state changes
    this.subscriptions.push(
      this.appStateService.pipelineState$.subscribe(pipelineState => {
        this.syncWithPipelineState(pipelineState);
      })
    );

    // Subscribe to navigation state changes
    this.subscriptions.push(
      this.appStateService.navigationState$.subscribe(navState => {
        this.syncWithNavigationState(navState);
      })
    );

    // Subscribe to loading state changes for API progress
    this.subscriptions.push(
      this.appStateService.loadingState$.subscribe(loadingState => {
        this.syncWithLoadingState(loadingState);
      })
    );
  }

  /**
   * Sync stepper with pipeline state changes
   */
  private syncWithPipelineState(pipelineState: any): void {
    // Mark steps as having data based on pipeline data
    const updatedSteps = [...this.steps];
    let hasChanges = false;

    // Check for Understanding step completion (market_research or lbc data)
    if (pipelineState.data.market_research || pipelineState.data.lbc) {
      const understandingStep = updatedSteps.find(step => step.id === 'understanding');
      if (understandingStep && !understandingStep.hasData) {
        understandingStep.hasData = true;
        understandingStep.isLoading = false;
        understandingStep.state = 'completed';
        hasChanges = true;
        console.log('✅ Understanding step completed');
      }
    }

    // Check for User Persona step completion (persona data)
    if (pipelineState.data.persona) {
      const personaStep = updatedSteps.find(step => step.id === 'persona');
      if (personaStep && !personaStep.hasData) {
        personaStep.hasData = true;
        personaStep.isLoading = false;
        personaStep.state = 'completed';
        hasChanges = true;
        console.log('✅ User Persona step completed');
      }
    }

    // Check for SWOT Analysis step completion (swot data)
    if (pipelineState.data.swot) {
      const swotStep = updatedSteps.find(step => step.id === 'swot');
      if (swotStep && !swotStep.hasData) {
        swotStep.hasData = true;
        swotStep.isLoading = false;
        swotStep.state = 'completed';
        hasChanges = true;
        console.log('✅ SWOT Analysis step completed');
      }
    }

    // Check for Feature List step completion (features data)
    if (pipelineState.data.features) {
      const featuresStep = updatedSteps.find(step => step.id === 'features');
      if (featuresStep && !featuresStep.hasData) {
        featuresStep.hasData = true;
        featuresStep.isLoading = false;
        featuresStep.state = 'completed';
        hasChanges = true;
        console.log('✅ Feature List step completed');
      }
    }

    // Check for Product Roadmap step completion (roadmap data)
    if (pipelineState.data.roadmap) {
      const roadmapStep = updatedSteps.find(step => step.id === 'roadmap');
      if (roadmapStep && !roadmapStep.hasData) {
        roadmapStep.hasData = true;
        roadmapStep.isLoading = false;
        roadmapStep.state = 'completed';
        hasChanges = true;
        console.log('✅ Product Roadmap step completed');
      }
    }

    if (hasChanges) {
      this.stepsSubject.next(updatedSteps);
    }
  }

  /**
   * Sync stepper with navigation state changes
   */
  private syncWithNavigationState(navState: any): void {
    if (navState.currentStepIndex !== this.currentStepIndexSubject.value) {
      this.currentStepIndexSubject.next(navState.currentStepIndex);
      this.updateStepStates();
    }
  }

  /**
   * Sync stepper with loading state changes for API progress
   */
  private syncWithLoadingState(loadingState: any): void {
    const updatedSteps = [...this.steps];
    let hasChanges = false;

    // Reset all loading states first
    updatedSteps.forEach(step => {
      if (step.isLoading) {
        step.isLoading = false;
        hasChanges = true;
      }
    });

    // Set loading state for the appropriate step based on current operation
    if (loadingState.isLoadingStep && loadingState.currentOperation) {
      const currentOperation = loadingState.currentOperation;

      // Map API operations to stepper steps
      const operationToStepMap: Record<string, string> = {
        'market_research': 'lbc',
        'lbc': 'persona',
        'persona': 'swot',
        'swot': 'features',
        'features': 'roadmap'
      };

      const stepId = operationToStepMap[currentOperation];
      if (stepId) {
        const step = updatedSteps.find(s => s.id === stepId);
        if (step) {
          step.isLoading = true;
          step.state = 'loading';
          hasChanges = true;
          console.log(`🔄 Step ${stepId} is now loading for API operation: ${currentOperation}`);
        }
      }
    }

    if (hasChanges) {
      this.stepsSubject.next(updatedSteps);
    }
  }

  // Observables for components to subscribe to
  get currentStepIndex$(): Observable<number> {
    return this.currentStepIndexSubject.asObservable();
  }

  get steps$(): Observable<StepperStep[]> {
    return this.stepsSubject.asObservable();
  }

  get currentStep$(): Observable<StepperStep> {
    return new Observable(observer => {
      this.currentStepIndex$.subscribe(index => {
        observer.next(this.steps[index]);
      });
    });
  }

  get canGoNext$(): Observable<boolean> {
    return this.currentStepIndex$.pipe(
      map(index => index < this.steps.length - 1)
    );
  }

  get canGoPrevious$(): Observable<boolean> {
    return this.currentStepIndex$.pipe(
      map(index => index > 0)
    );
  }

  // Getters
  get currentStepIndex(): number {
    return this.currentStepIndexSubject.value;
  }

  get currentStep(): StepperStep {
    return this.steps[this.currentStepIndex];
  }

  get allSteps(): StepperStep[] {
    return [...this.steps];
  }

  get totalSteps(): number {
    return this.steps.length;
  }

  // Navigation methods
  nextStep(): boolean {
    if (this.canGoNext()) {
      const newIndex = this.currentStepIndex + 1;
      const newStep = this.steps[newIndex];

      console.log(`➡️ Moving to next step: ${newIndex} (${newStep.id})`);

      this.currentStepIndexSubject.next(newIndex);
      this.updateStepStates();

      // Update centralized navigation state
      this.appStateService.updateNavigationState({
        currentStepIndex: newIndex,
        currentStepId: newStep.id,
        canGoNext: this.canGoNext(),
        canGoPrevious: this.canGoPrevious()
      });StepperService

      // Update current step in pipeline state for chat context
      this.updatePipelineCurrentStep(newStep.id);

      return true;
    }
    return false;
  }

  previousStep(): boolean {
    if (this.canGoPrevious()) {
      const newIndex = this.currentStepIndex - 1;
      const newStep = this.steps[newIndex];

      console.log(`⬅️ Moving to previous step: ${newIndex} (${newStep.id})`);

      this.currentStepIndexSubject.next(newIndex);
      this.updateStepStates();

      // Update centralized navigation state
      this.appStateService.updateNavigationState({
        currentStepIndex: newIndex,
        currentStepId: newStep.id,
        canGoNext: this.canGoNext(),
        canGoPrevious: this.canGoPrevious()
      });

      // Update current step in pipeline state for chat context
      this.updatePipelineCurrentStep(newStep.id);

      return true;
    }
    return false;
  }

  goToStep(index: number): boolean {
    if (index >= 0 && index < this.steps.length) {
      const previousIndex = this.currentStepIndexSubject.value;
      const newStep = this.steps[index];

      console.log(`🎯 Navigating from step ${previousIndex} to step ${index} (${newStep.id})`);

      this.currentStepIndexSubject.next(index);
      this.updateStepStates();

      // Update centralized navigation state
      this.appStateService.updateNavigationState({
        currentStepIndex: index,
        currentStepId: newStep.id,
        canGoNext: this.canGoNext(),
        canGoPrevious: this.canGoPrevious()
      });

      // Update current step in pipeline state for chat context
      this.updatePipelineCurrentStep(newStep.id);

      return true;
    }
    return false;
  }

  goToStepById(stepId: string): boolean {
    const index = this.steps.findIndex(step => step.id === stepId);
    console.log(`🎯 Going to step by ID: ${stepId} (index: ${index})`);
    return this.goToStep(index);
  }

  // Validation methods
  canGoNext(): boolean {
    return this.currentStepIndex < this.steps.length - 1;
  }

  canGoPrevious(): boolean {
    return this.currentStepIndex > 0;
  }

  // Step state management
  markStepAsCompleted(index: number): void {
    if (index >= 0 && index < this.steps.length) {
      this.steps[index].state = 'completed';
      this.stepsSubject.next([...this.steps]);
    }
  }

  markCurrentStepAsCompleted(): void {
    this.markStepAsCompleted(this.currentStepIndex);
  }

  /**
   * Mark a step as having data
   */
  markStepHasData(stepIndex: number): void {
    if (stepIndex >= 0 && stepIndex < this.steps.length) {
      this.steps[stepIndex].hasData = true;
      this.stepsSubject.next([...this.steps]);
    }
  }

  /**
   * Check if a step has data
   */
  stepHasData(stepId: string): boolean {
    const step = this.steps.find(s => s.id === stepId);
    return step?.hasData || false;
  }

  /**
   * Reset all steps to initial state (for recent project loading)
   */
  resetAllSteps(): void {
    this.steps.forEach((step, index) => {
      step.state = index === 0 ? 'active' : 'inactive';
      step.hasData = false;
      step.isLoading = false;
    });
    this.stepsSubject.next([...this.steps]);
    console.log('🔄 All steps reset to initial state');
  }

  /**
   * Check if navigation should call API or just move between steps
   */
  shouldCallApiForStep(stepId: string): boolean {
    return !this.stepHasData(stepId);
  }

  resetSteps(): void {
    this.steps.forEach((step, index) => {
      step.state = index === 0 ? 'active' : 'inactive';
    });
    this.currentStepIndexSubject.next(0);
    this.stepsSubject.next([...this.steps]);
  }

  private updateStepStates(): void {
    this.steps.forEach((step, index) => {
      // Don't override loading state if step is currently loading
      if (step.isLoading) {
        step.state = 'loading';
      } else if (index < this.currentStepIndex || step.hasData) {
        step.state = 'completed';
      } else if (index === this.currentStepIndex) {
        step.state = 'active';
      } else {
        step.state = 'inactive';
      }
    });
    this.stepsSubject.next([...this.steps]);
  }

  // Utility methods
  getStepByIndex(index: number): StepperStep | null {
    return this.steps[index] || null;
  }

  getStepById(stepId: string): StepperStep | null {
    return this.steps.find(step => step.id === stepId) || null;
  }

  getStepIndexById(stepId: string): number {
    return this.steps.findIndex(step => step.id === stepId);
  }

  isFirstStep(): boolean {
    return this.currentStepIndex === 0;
  }

  isLastStep(): boolean {
    return this.currentStepIndex === this.steps.length - 1;
  }

  getProgress(): number {
    return Math.round(((this.currentStepIndex + 1) / this.steps.length) * 100);
  }



  /**
   * Get stepper step ID that corresponds to an API pipeline step
   */
  getStepperIdForApiStep(apiStep: PipelineStep): string | null {
    const apiToStepperMap: Record<PipelineStep, string> = {
      'market_research': 'understanding',
      'lbc': 'understanding',
      'persona': 'persona',
      'swot': 'swot',
      'features': 'features',
      'roadmap': 'roadmap'
    };

    return apiToStepperMap[apiStep] || null;
  }

  /**
   * Get the next API step that should be called based on current stepper step
   * Maps stepper steps to API current_step parameter for next button clicks
   */
  getNextApiStepForCurrentStep(): PipelineStep | null {
    const currentStep = this.currentStep;

    // Map stepper steps to the API current_step parameter
    // This determines what API call to make when user clicks next
    const stepperToApiMap: Record<string, PipelineStep> = {
      'understanding': 'lbc', // Step 1: Understanding → call API with current_step: 'lbc' to get persona data
      'persona': 'persona', // Step 2: User Persona → call API with current_step: 'persona' to get swot data
      'swot': 'swot', // Step 3: SWOT Analysis → call API with current_step: 'swot' to get features data
      'features': 'features', // Step 4: Feature List → call API with current_step: 'features' to get roadmap data
      'roadmap': 'roadmap' // Step 5: Roadmap → call API with current_step: 'roadmap' (final step)
    };

    return stepperToApiMap[currentStep.id] || null;
  }

  /**
   * Update stepper based on pipeline API response step
   * Advances to the next logical step after API completion
   */
  updateStepFromApiResponse(apiStep: PipelineStep): void {
    console.log('🔄 Processing API response for step:', apiStep);

    // Map API response steps to the NEXT stepper step that should be shown
    // This ensures progression after API completion
    const apiToNextStepMap: Record<PipelineStep, string> = {
      'market_research': 'lbc', // Market research completes → stay on understanding (initial step)
      'lbc': 'persona', // LBC completes → advance to persona step
      'persona': 'swot', // Persona completes → advance to swot step
      'swot': 'features', // SWOT completes → advance to features step
      'features': 'roadmap', // Features completes → advance to roadmap step
      'roadmap': 'roadmap' // Roadmap completes → stay on roadmap (final step)
    };

    const nextStepId = apiToNextStepMap[apiStep];
    if (nextStepId) {
      const nextStepIndex = this.steps.findIndex(step => step.id === nextStepId);
      if (nextStepIndex !== -1) {
        // Mark all previous steps as completed and having data
        for (let i = 0; i <= nextStepIndex; i++) {
          this.markStepAsCompleted(i);
          this.markStepHasData(i);
        }

        // Navigate to the next step
        this.goToStep(nextStepIndex);

        console.log('✅ Successfully processed API response and advanced to next step:', {
          apiStep,
          nextStepId,
          nextStepIndex,
          message: `Advanced from API step '${apiStep}' to stepper step '${nextStepId}'`
        });
      }
    }
  }

  /**
   * Configure stepper based on completed pipeline steps (for recent projects)
   * Marks appropriate steps as completed and navigates to the correct step
   */
  configureFromCompletedSteps(completedSteps: PipelineStep[]): void {
    console.log('🔄 Configuring stepper from completed steps:', completedSteps);

    // Reset all steps to initial state
    this.resetAllSteps();

    // Map pipeline steps to stepper step indices
    const pipelineToStepperMap: Record<PipelineStep, string> = {
      'market_research': 'lbc',
      'lbc': 'persona', // Both market_research and lbc show data on understanding step
      'persona': 'swot',
      'swot': 'features',
      'features': 'roadmap',
      'roadmap': 'summary'
    };

    // Determine which stepper steps should be marked as completed
    const completedStepperSteps = new Set<string>();
    completedSteps.forEach(pipelineStep => {
      const stepperStep = pipelineToStepperMap[pipelineStep];
      if (stepperStep) {
        completedStepperSteps.add(stepperStep);
      }
    });

    // Mark stepper steps as completed and having data
    this.steps.forEach((step, index) => {
      if (completedStepperSteps.has(step.id)) {
        this.markStepAsCompleted(index);
        this.markStepHasData(index);
      }
    });

    // Determine where to navigate based on completion status
    let targetStepIndex = 0; // Default to understanding step

    if (completedSteps.length === 0) {
      // No completed steps - start at understanding
      targetStepIndex = 0;
    } else {
      // Find the furthest completed step and navigate there
      const stepOrder = ['understanding', 'persona', 'swot', 'features', 'roadmap'];
      for (let i = stepOrder.length - 1; i >= 0; i--) {
        if (completedStepperSteps.has(stepOrder[i])) {
          targetStepIndex = this.steps.findIndex(step => step.id === stepOrder[i]);
          break;
        }
      }
    }

    // Navigate to the target step
    this.goToStep(targetStepIndex);

    console.log('✅ Successfully configured stepper from completed steps:', {
      completedSteps,
      completedStepperSteps: Array.from(completedStepperSteps),
      targetStepIndex,
      targetStepId: this.steps[targetStepIndex]?.id
    });
  }

  /**
   * Restore stepper state from stored pipeline data
   * CORRECTED: Maps data to correct display steps
   */
  restoreFromStoredData(pipelineData: any): void {
    console.log('🔄 Restoring stepper state from stored data:', pipelineData);

    // CORRECTED: Mark steps as having data based on stored pipeline data
    if (pipelineData.market_research || pipelineData.lbc) {
      this.markStepHasData(0); // understanding - CORRECTED: lbc data shows on understanding
    }

    if (pipelineData.persona) {
      this.markStepHasData(1); // persona
    }

    if (pipelineData.swot) {
      this.markStepHasData(2); // swot
    }

    if (pipelineData.features) {
      this.markStepHasData(3); // features
    }

    if (pipelineData.roadmap) {
      this.markStepHasData(4); // roadmap
    }

    // Navigate to the appropriate step based on current_step
    this.navigateToStoredStep(pipelineData.current_step);

    console.log('✅ Successfully restored stepper state');
  }

  /**
   * Navigate to the appropriate step based on stored current_step
   * CORRECTED: Maps API steps to their correct display steps
   */
  private navigateToStoredStep(currentStep: string | null): void {
    if (!currentStep) return;

    // CORRECTED: Map API steps to correct stepper step indices
    const stepMapping: Record<string, number> = {
      'market_research': 0, // understanding (index 0)
      'lbc': 0, // understanding (index 0) - CORRECTED
      'persona': 1, // persona (index 1)
      'swot': 2, // swot (index 2)
      'features': 3, // features (index 3)
      'roadmap': 4, // roadmap (index 4)
    };

    const stepIndex = stepMapping[currentStep];
    if (stepIndex !== undefined) {
      // Mark previous steps as completed
      for (let i = 0; i < stepIndex; i++) {
        this.markStepAsCompleted(i);
      }

      // Navigate to the current step
      this.goToStep(stepIndex);

      console.log('🎯 Navigated to restored step:', {
        currentStep,
        stepIndex,
        stepId: this.steps[stepIndex]?.id
      });
    }
  }

  /**
   * Update pipeline current step for chat context
   */
  private updatePipelineCurrentStep(stepId: string): void {
    // Map stepper step IDs to pipeline steps
    const stepToPipelineMap: Record<string, PipelineStep> = {
      'understanding': 'lbc',
      'persona': 'persona',
      'swot': 'swot',
      'features': 'features',
      'roadmap': 'roadmap'
    };

    const pipelineStep = stepToPipelineMap[stepId];
    if (pipelineStep) {
      // Update pipeline state through app state service
      const currentPipelineState = this.appStateService.pipelineState;
      const updatedState = {
        ...currentPipelineState,
        current_step: pipelineStep
      };

      console.log(`🔄 Updating pipeline current_step from ${currentPipelineState.current_step} to: ${pipelineStep} (stepper: ${stepId})`);
      this.appStateService.updatePipelineState(updatedState);
      console.log(`✅ Pipeline state updated successfully`);
    } else {
      console.warn(`⚠️ No pipeline step mapping found for stepper step: ${stepId}`);
    }
  }

  /**
   * Initialize pipeline state with current step on service creation
   */
  private initializePipelineState(): void {
    const currentIndex = this.currentStepIndexSubject.value;
    const currentStep = this.steps[currentIndex];

    if (currentStep) {
      console.log(`🚀 Initializing pipeline state with current step: ${currentStep.id} (index: ${currentIndex})`);
      this.updatePipelineCurrentStep(currentStep.id);
    }
  }
}
